;**********************************************************************************************************************************
;
;	Cadence Allegro Batch FLIR Update - Process Multiple .BRD Files
;
;	This program will:
;	1. Scan a directory for .BRD files
;	2. Load each .BRD file one by one
;	3. Run FLIR footprint updates on each file
;	4. Save and close each file
;	5. Generate a summary report
;
;	Usage: Load this script and run 'batch_flir_update'
;
;**********************************************************************************************************************************
;
; load("e:/pads/Allegro/Skill/ai/Batch_FLIR_Update.il")
;
;**********************************************************************************************************************************

; Register the main command
axlCmdRegister("batch_flir_update" `batch_flir_update ?cmdType "interactive")

;******************************************************
;
; batch_flir_update - Main routine for batch processing
;
;******************************************************

procedure(batch_flir_update()
  prog((source_dir brd_files processed_count failed_count summary_list current_file result)
    
    printf("=== Batch FLIR Update Tool ===\n")
    
    ; Get source directory from user
    source_dir = axlUIFileSelDir("Select directory containing .BRD files:")
    unless(source_dir
      printf("No directory selected. Exiting.\n")
      return(nil)
    )
    
    printf("Selected directory: %s\n" source_dir)
    
    ; Find all .BRD files in the directory
    brd_files = get_brd_files_in_directory(source_dir)
    unless(brd_files
      axlUIConfirm("No .BRD files found in the selected directory.")
      return(nil)
    )
    
    printf("Found %d .BRD files to process\n" length(brd_files))
    
    ; Confirm processing
    unless(axlUIYesNo(sprintf(nil "Found %d .BRD files in:\n%s\n\nDo you want to process all files with FLIR updates?" length(brd_files) source_dir))
      printf("Processing cancelled by user\n")
      return(nil)
    )
    
    ; Initialize counters
    processed_count = 0
    failed_count = 0
    summary_list = nil
    
    ; Process each .BRD file
    foreach(current_file brd_files
      printf("\n=== Processing file %d of %d: %s ===\n" (processed_count + failed_count + 1) length(brd_files) current_file)
      
      result = process_single_brd_file(current_file)
      
      if(result then
        processed_count = processed_count + 1
        summary_list = cons(sprintf(nil "SUCCESS: %s" current_file) summary_list)
        printf("Successfully processed: %s\n" current_file)
      else
        failed_count = failed_count + 1
        summary_list = cons(sprintf(nil "FAILED: %s" current_file) summary_list)
        printf("Failed to process: %s\n" current_file)
      )
    )
    
    ; Display final summary
    display_batch_summary(processed_count failed_count summary_list)
    
    printf("\n=== Batch Processing Complete ===\n")
    printf("Total files processed: %d\n" processed_count)
    printf("Total files failed: %d\n" failed_count)
    
  ) ; End prog
) ; End procedure

;******************************************************
;
; get_brd_files_in_directory - Find all .BRD files in directory
;
;******************************************************

procedure(get_brd_files_in_directory(directory)
  prog((file_list brd_files full_path)
    
    ; Get list of all files in directory
    file_list = getDirFiles(directory)
    unless(file_list
      printf("Could not read directory: %s\n" directory)
      return(nil)
    )
    
    brd_files = nil
    
    ; Filter for .BRD files and create full paths
    foreach(file file_list
      when(rexMatchp("\\.brd$" file "i")  ; Case insensitive match for .brd extension
        full_path = strcat(directory "/" file)
        brd_files = cons(full_path brd_files)
      )
    )
    
    ; Reverse to maintain original order
    return(reverse(brd_files))
  ) ; End prog
) ; End procedure

;******************************************************
;
; process_single_brd_file - Process one .BRD file
;
;******************************************************

procedure(process_single_brd_file(brd_file_path)
  prog((original_design success)
    
    ; Store current design (if any)
    original_design = axlCurrentDesign()
    
    ; Close current design if open
    when(original_design
      printf("Closing current design: %s\n" original_design)
      axlClose()
    )
    
    ; Open the .BRD file
    printf("Opening: %s\n" brd_file_path)
    unless(axlOpen(brd_file_path)
      printf("ERROR: Failed to open file: %s\n" brd_file_path)
      return(nil)
    )
    
    printf("Successfully opened: %s\n" brd_file_path)
    
    ; Run FLIR footprint update
    printf("Running FLIR footprint update...\n")
    success = run_flir_update_on_current_design()
    
    ; Save the file
    if(success then
      printf("Saving file: %s\n" brd_file_path)
      axlSave()
      printf("File saved successfully\n")
    else
      printf("FLIR update failed, file not saved\n")
    )
    
    ; Close the file
    axlClose()
    printf("File closed\n")
    
    return(success)
  ) ; End prog
) ; End procedure

;******************************************************
;
; run_flir_update_on_current_design - Run FLIR update on current design
;
;******************************************************

procedure(run_flir_update_on_current_design()
  prog((result)
    
    ; Check if flir_footprint_update function exists
    unless(fboundp('flir_footprint_update)
      printf("ERROR: flir_footprint_update function not found. Please load FLIR_Footprint_update.il first.\n")
      return(nil)
    )
    
    ; Run the FLIR footprint update
    ; Note: This assumes the FLIR update function handles user interaction appropriately
    ; You may need to modify this based on how you want to handle the expansion selection
    ; for batch processing (e.g., use a default value or prompt once for all files)
    
    result = flir_footprint_update()
    
    return(result)
  ) ; End prog
) ; End procedure

;******************************************************
;
; display_batch_summary - Display processing summary
;
;******************************************************

procedure(display_batch_summary(processed_count failed_count summary_list)
  prog((summary_text)
    
    summary_text = sprintf(nil "=== BATCH PROCESSING SUMMARY ===\n\nSuccessfully processed: %d files\nFailed to process: %d files\n\nDetailed Results:\n" processed_count failed_count)
    
    ; Add each file result to summary
    foreach(item reverse(summary_list)
      summary_text = strcat(summary_text item "\n")
    )
    
    ; Display summary in dialog
    axlUIConfirm(summary_text)
    
    ; Also print to console
    printf("%s\n" summary_text)
    
  ) ; End prog
) ; End procedure

; Load message
printf("Batch FLIR Update Script loaded. Run 'batch_flir_update' to process multiple .BRD files.\n")
printf("Note: Make sure FLIR_Footprint_update.il is loaded first.\n")
