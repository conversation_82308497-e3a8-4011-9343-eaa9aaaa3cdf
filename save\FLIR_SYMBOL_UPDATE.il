 ;**********************************************************************************************************************************
 ;
 ;	Cadence Allegro flir - Update DISPLAY_TOP Layer
 ;
 ;	This program will delecte any shape on the display top layer, and take the DFA_BOUND_TOP layer increase the shape by
 ;	"L" = .125MM "N" .25MM M = .5MM
 ;
 ;	
 ;
 ;********************************************************************************************************************************
 ;
 ;
 ; To load the program into skill
 ; Type SKILL on the allegro command line
 ;
 ; 
 ;load "e:/pads/Allegro/Skill/ai/FLIR_SYMBOL_UPDATE.il"
 ;
 ; 
  
 axlCmdRegister("Footprint_Main_Routine" `Footprint_Main_Routine ?cmdType "interactive") 


;**************************************************************************************************
;
;  Define function  - Toolbar_Main_Routine
; 
; Description - Main Routine for toolbar program 
; 
; Variables used in the routine are:
;
;   Visible_List - This defines current Class/Subclass visibility
;   Available_Layers - This defines the DRC subclasses
;   DRC_Layer - This defines the first subclass in the DRC class
;   Last_Layer - This defines the last subclass in the DRC class
;   Classes - List of all classes
; 
;
;**************************************************************************************************


(defun Footprint_Main_Routine ()
;
;Set starting variables
;
Increase_Value = .125
coordinates = 0
expansion = 0

; Check if we're in the correct editor mode
   
   
   unless(axlDesignType(t) == "PACKAGE"
   axlUIConfirm("This script must be run in Package Symbol Editor mode.")
   return(nil)
   );
   
   ;
   ; How to see if a symbol is loaded.
   ; 


  


Create_Form()											 ; Call CreateForm Function

								;  Display Form
Set_DataBase_Units(0)										; set database units to MM
Set_Increase_Value(0)										; set increase value to 0
Turn_off_all_layers()										; Turn of fall layers. 
Set_Reset_Display_Top(1)
Set_Reset_DFA_Bound_Top(1)			
myform=axlFormCreate( (gensym) form_file nil 'Form_Action1 t)					;  Set myform to 
axlFormDisplay(myform)		
axlFormSetField( myform "Least-.125MM" t)							; Enable Least
axlUIWPerm(myform t)										; to make form perminent


);END Foot_Printe Main_Routine

;**************************************************************************************************
;
;  Define function  - Form_Action
; 
; Description - Form_Action is called any time changes are made on the screen
; 
; Inputs or outputs
;
;**************************************************************************************************


(defun Form_Action1 (myform)
printf(myform->curField)
;(let (t1 item index field cnt)(printf "field/value %L = %L (int %L\n)" 
;myform->curField myform->curValue, myform->curValueInt )
;(printf "doneState %L\n" myform->doneState )
Field = myform->curField

case(myform->curField
		

;
;		Radio Field for Display_top increase
;

		("Least"
		 Set_Increase_Value(1)
		;Radio Field 
		;Enter Action Here
		
		)
		("nominal"
		Set_Increase_Value(2)
		;Radio Field 
		;Enter Action Here
		
		)
		("most"
		Set_Increase_Value(3)
		;Radio Field 
		;Enter Action Here
		
		)
		("Start"								
		Update_Footprint()
		;Radio Field 
		;Enter Action Here
		)	
		
		
		("Exit"								;Go to start of program
				
		End_Program()
		
		;Radio Field 
		;Enter Action Here
		);End 	
    ); End Case
		
); End Form_Action1


;**************************************************************************************************
;
;	Description - Delete Display_top data. Copy DFA_BOUND and increase size per Increase value
;
;	
;**************************************************************************************************
(defun Update_Footprint ()



     symbol = axlCurrentDesign()						; Get current symbol name
     ;
     ; display Symbol Name 
     ;
     axlFormSetField( myform "input1" symbol)    
     ;
     ; Delete all shapes on DISPLAY_TOP layer
     ;
     
         shapes_to_delete = axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP")
         if(shapes_to_delete then
           axlDeleteObject(shapes_to_delete)
           println("Deleted all shapes on DISPLAY_TOP layer")
         else
           println("No shapes found on DISPLAY_TOP layer - OK ")
          
    	 );End If
  
  
  
  
  
  ;   
  ; Get shape on DFA_BOUND layer
  ;
    shapes_to_copy = axlDBGetShapes("PACKAGE GEOMETRY/DFA_BOUND_TOP")
  
  ;
  ; If no shapes output message or if more than one.
  ;
  	if( length(shapes_to_copy) == 0  then
  	  axlUIConfirm("No shape found on DFA_BOUND_TOP layer")
   	; exit() 
   	 );End If
 
   	 if( length(shapes_to_copy) >= 2  then
	  axlUIConfirm("Multiple shapes found on DFA_BOUND_TOP layer")
   	; exit()
  	 );End If
   
   ;
   ; Copy shape on DFA_BOUND_LAYER 
   ;
   New_Shape = axlCopyObject(shapes_to_copy,?angle 0)
   
   ;
   ; If no new shape then send massege and exit. 
   ;     
     if((New_Shape == nil) then
      axlUIConfirm("Shape not found to copy from layer DFA_BOUND_TOP layer")
   ;  exit()
   	); Endif
   ;
   ;Change Layer to display top
   ;
   New_Shape = axlChangeLayer(New_Shape "PACKAGE GEOMETRY/DISPLAY_TOP")

     if((New_Shape == nil) then
      axlUIConfirm("Shape not changed to DISPLAY_TOP layer")
     ); Endif


   ;
   ; Expand shape on DISPLAY_TOP
   ;
   
   
   ;
   ; Shapes do not have a way to expand in SKILL so we need to create a polygon
   ;
  
  ;
  ; Get Shape on Display Top Layer. 
  ; Get dbid for the shape or shapes. Should Be 1 Shape.
  ;
   
   ;
   ; Get tha sape in dbid . Car removed parentheses
   ;
   
    shape = car(axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP")) 
  
  ;
  ; Convert shape to polygon
  
    poly = axlPolyFromDB(shape)
  
  ;
  ;Increase all polygon side by Increse Value
  ; 
  exp_poly = car(axlPolyExpand(poly Increase_Value 'None))
    
   ;
   ; Copy polygon back to shape 
   ;
   new_shape = axlDBCreateShape(exp_poly, t, "PACKAGE GEOMETRY/DISPLAY_TOP")
   
  	
    Turn_on_all_layers() 
    axlSaveDesign()
    
    
    return(t)
  

	 
    	 
      
); End defun Update_Fooptrint

 
 ;**************************************************************************************************
 ;
 ;	Description - End Program
 ;	
 ;**************************************************************************************************
(defun  End_Program ()
; 
; Inform user
;

     println(printf(Nil "Footprint updated with oversize value '%n'." Increase_Value))
     axlUIConfirm(printf("Footprint updated with oversize value '%n'." Increase_Value))
    
;
; Close form and exit
;
    axlFormClose(form)
    exit()
  
    );End defun End_Program

	
;**************************************************************************************************
;
;	Description - Turn on display layer
;	
;**************************************************************************************************

(defun Change_Layer_Color ()


q = axlLayerGet("Package Geometry/DISPLAY_top")
q->color = 1
q->pattern = 0 ; solid pattern
q->visibility = t
axlLayerSet(q)

); End defun Change_Layer_Color

;**************************************************************************************************
;
;	Description - Turn off all layers
;
;	
;**************************************************************************************************
(defun Turn_off_all_layers ()

	axlVisibleDesign(nil) 								; Turn off all layers

); End defun Turn_Off_All_Layers

;**************************************************************************************************
;
;	Description - Turn off all layers
;
;	
;**************************************************************************************************
(defun Turn_on_all_layers ()

	axlVisibleDesign(t)								; Turn off all layers

); End defun Turn_ON_All_layers


;**************************************************************************************************
;
;	Description - Set display_Top on or off 
;
;	Calling Fomat - Set_Reset_Display_Top
;	Return
;
;**************************************************************************************************
(defun Set_Reset_Display_Top (number)
if(number then  
   axlVisibleLayer( "Package Geometry/DISPLAY_top" t) 
   else
    axlVisibleLayer( "Package Geometry/DISPLAY_top" nil) 
); End if		
		axlFlushDisplay() 												; Redisplay
		axlShell("redisplay") 
); End defun Set_Reset_Display_Top



;**************************************************************************************************
;
;	Description - Set Place_Bound Top on or off 
;
;	Calling Fomat - Set_Reset_Place_Bound_Top
;	Return
;
;**************************************************************************************************
(defun Set_Reset_DFA_Bound_Top (number)
if(number then  
	axlVisibleLayer( "Package Geometry/DFA_Bound_Top" t)
  else
    axlVisibleLayer( "Package Geometry/DFA_Bound_Top" nil) 
); End if		
		axlFlushDisplay() 												; Redisplay
		axlShell("redisplay") 
); End defun Set_Reset_Place_Bound_Top




;**************************************************************************************************
;
; Description - Define Function to set initial increase value
;
; Calling Format -  Set_Increase_Value()
;
; Return 
;
;**************************************************************************************************
(defun Set_Increase_Value (Unit)

							; set the 0th value as unit 
case(Unit 
         ("Least-.125MM"
         axlFormSetField( myform "Least-.125MM" t)
         Increase_Value = .125
        ) 
	 ("NOMINAL-.25MM"
          axlFormSetField( myform "NOMINAL-.25MM" t)								; Enable Mils
         Increase_Value = .25
 	 ) 
 	 ("MOST-.5MM" 
          axlFormSetField( myform "MOST-.5MM" t)								; Enable Inches
         Increase_Value = .5
 	 ) 
        );end case 
);End defun Set_Increase_Value

;**************************************************************************************************
;
; Description - Define Function to set database units
;
; Calling Format -  Set_DataBase_Units()
;
; Return 
;
;**************************************************************************************************
(defun Set_DataBase_Units (Number)
          
	  axlDBChangeDesignUnits("millimeters" 4)
);End defun Set_DataBase_Units



;**************************************************************************************************
;
; Define Function to create a Form file in BNF Format.
;
;**************************************************************************************************

(defun Create_Form ()												; Define a function
drain()														; Writes out all the characters that are in the output buffer of a port.
form_file = "./output_form.form"										; Setup file name
myform = outfile(form_file "w")										; open form_file for writing

fprintf(myform "FILE_TYPE=FORM_DEFN VERSION=2\n")
fprintf(myform "FORM AUTOGREYTEXT\n")
fprintf(myform "FIXED\n")
fprintf(myform "PORT 40 60\n")
fprintf(myform "HEADER \"FOOTPRINT UPDATE\"\n\n")
fprintf(myform "TILE\n\n")

fprintf(myform "## Text in FORM ## \n")
fprintf(myform "TEXT \"Update DISPLAY_TOP from DRA_BOUND \"\n")
fprintf(myform "TLOC 1 1\n")
fprintf(myform "ENDTEXT\n")

fprintf(myform "## Text Field in Form## \n")
fprintf(myform "TEXT \"FOOTPRINT NAME -\"\n")
fprintf(myform "FLOC 1 3\n")
fprintf(myform "TGROUP \"Form1\"\n")
fprintf(myform "ENDTEXT\n\n")


fprintf(myform "## Group Definition in Form## \n")
fprintf(myform "GROUP \"DISPLAY_TOP\"\n")
fprintf(myform "GLOC 1 3\n")
fprintf(myform "FSIZE 10 8\n")
fprintf(myform "ENDGROUP\n\n")
fprintf(myform "## RadioButton Field in Group## \n")
fprintf(myform "FIELD LEAST-.125MM\n")
fprintf(myform "FLOC 2 5\n")
fprintf(myform "CHECKLIST \"LEAST-.125MM\"  \"rg\"\n")
fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
fprintf(myform "ENDFIELD \n\n")
fprintf(myform "## RadioButton Field in Group## \n")
fprintf(myform "FIELD NOMINAL-.25MM\n")
fprintf(myform "FLOC 2 7\n")
fprintf(myform "CHECKLIST \"NOMINAL-.25MM\"  \"rg\"\n")
fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
fprintf(myform "ENDFIELD \n\n")
fprintf(myform "## RadioButton Field in Group## \n")
fprintf(myform "FIELD MOST-.5MM\n")
fprintf(myform "FLOC 2 9\n")
fprintf(myform "CHECKLIST \"MOST-.5MM\"  \"rg\"\n")
fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
fprintf(myform "ENDFIELD \n\n")

fprintf(myform "## Input Field in Form## \n")
fprintf(myform "FIELD Input1\n")
fprintf(myform "FLOC 15 3\n")
fprintf(myform "FSIZE 5 2\n")
fprintf(myform "STRFILLIN 20 40\n")
fprintf(myform "FGROUP \"Form1\"\n")
fprintf(myform "VALUE \"\"\n")
fprintf(myform "ENDFIELD \n\n\n")

fprintf(myform "## Button Field in FORM ## \n")
fprintf(myform "FIELD Start\n")
fprintf(myform "FLOC 2 13\n")
fprintf(myform "MENUBUTTON \"Start\" 10 3\n")
fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
fprintf(myform "ENDFIELD \n\n")

fprintf(myform "## Button Field in FORM ## \n")
fprintf(myform "FIELD Exit\n")
fprintf(myform "FLOC 15 13\n")
fprintf(myform "MENUBUTTON \"Ok\" 10 3\n")
fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
fprintf(myform "ENDFIELD \n\n")

fprintf(myform "ENDTILE\n\n")
fprintf(myform "ENDFORM\n\n")
close(myform)
)


