;; =========================================================================
;; FLIR Footprint Update Script for Cadence Allegro
;; - Extracts all symbols from current PCB into "original_library" directory
;; - Creates directory if it doesn't exist
;; - Handles duplicate symbols
;; - Provides progress feedback during operation
;;
;; - We keep track of the PCB name and footprint name so we know which PCB file we 
;; - saved the footprint from. Either the variable Unique_Footprints is setto nil or 
;; - reads the csv file
;; - 
;; -  The format for the file name is :
;; -  1 = PCB FileName 
;; -  2 = footprint_name 
;; -  3 = Place_Bound_Shape - does the shape exist
;; -  4 = Min_Height - value or nil if it does not exist
;; -  5 = Max_Height - value or nil if it does not exist
;; -  6 = DFA_Bound_Shape - does the shape exist
;; -  7 = DISPLAY_TOP - did we create the shape on the display top layer. 
;;
;; Rev A - 7-9-2025 Initiail release
;; =========================================================================
;load("e:/pads/Allegro/Skill/ai/FLIR_Footprint_update.il")
;
;
;

;
; Explain how to use the structure, read it from a file or initialize it, add items and
; save it to a file.

; 1. First define the structure  - Create_File_structure()
; 2. Second create a component(this contains a pointer to a structure template) can use ->
;     to access the fields in a component - create_pcb_component(footprint_name pcb_name
;     place_bound max_height dfa_bound display_top)
; 3. Make a table so we can create and/or get components - create_component_collection() returns pointer
;     to a data structure that contains the structure (component) 
; 4. We can display the structure filed names and data - display_component(component)
; 5. We can update the fields in the structure  - update_component_field(component
;    Footprint_Key field_name new_value)
; 6. We can get the value of a field - get_component_field(component field_name)
;
; We not have a data structure called component and functions to write or read any fileds in the structure
; now we need to create a table to hold multiple structures of component
;
; 7. We now need to create a table that contains components and count to start -
;    create_component_collection() returns collection
; 8. Its time to read the CSV file if it exists.  load_collection_from_csv(filename) nil if no file
; 9. Ok. we have a table called collection to store data. 
; 10.Read and save footprints and update table. Go thru all footprints and then write the csv file. 
; 
; List functions to work with collection
;
; add_component_to_collection(collection component)
;
; get_collection_size(collection)
; 
; get_component_by_index(collection index)
;
; find_components_by_footprint(collection footprint_name)
;
; update_component_field(component field_name new_value)
;
; get_component_field(component field_name)
;
; 11.  Save the table to csv file
;


;********************************************************************
;
;	Structure routines
;
;********************************************************************

; Create_File_structure - creates a structure called PCB Component with the 7 items above. This is a template
; with the 7 values so you can adcess them by name.
;
; When you define a structure SKILL creates the following functions
;
; make_pcb_component() - Constructor (creates new instance) - empty component. .
;
; pcb_component_p(obj) - Type checker (returns t if obj is pcb_component)
;
; Field accessors - Access fields using arrow notation (->)


; pcb_component_p(obj) - Type checker (returns t if obj is pcb_component)
; Field accessors - Access fields using arrow notation (->)


; create_pcb_component( pcb_name Footprint_name place_bound max_height dfa_bound display_top
; - creates a variable and returns that variable called component(its local). It also calls
;
; make_pcb_component() is created by skill (see above).
;

;
; display_component(component) - displays the passed structure components 7 items
;



; update_component_field(component field_name new_value) - updates the provided field name with the new value
;  in teh passed component structure



 ;
; get_component_field(component field_name) gets the current field_name valur from the current component.
;  returns nil if no value to pass. 
;;
;; definitions for me
;;

;; 1  - structure   -  creates a group of the same variables so they can be used later.  
;; 2  - component   -  pointer to a structure
;; 3  - collection  - 

;;Think of it as:

;;collection = Base pointer to a control block
;;collection["components"] = Pointer to linked list of components
;;collection["count"] = Counter (like your COMPONENT_COUNT)

;; 4  - constructor - 
;; 5  - table -     - used to store multiple components


; =========================================================================
; STRUCTURE COLLECTION MANAGEMENT
; =========================================================================
;
;create_component_collection() - Makes a table called component_collection
; and sets it to collection
; set it components ??? and no count. 

;
;add_component_to_collection(collection component) - set collection to table and component 
; to values
;
;
; get_collection_size(collection) - gets the size of the passed collection
; 
;
; find_components_by_footprint(collection footprint_name)
;   

; =========================================================================
; CSV EXPORT FUNCTIONALITY
; =========================================================================
;
; save_collection_to_csv(collection filename)
;
; Routine to save the collection by adding a header and then each entry in a CSV file.
;

;
; display_collection_summary(collection)


;;; =========================================================================
;;; UTILITY FUNCTIONS
;;; =========================================================================
;
; load_collection_from_csv(filename) - reads csv file into collection ansd returns the 
;  new variable
;
;
; import_csv_to_collection(existing_collection filename) - import and merge existing collection 
;  into csv file.
;
; parse_csv_line(line) - 

; 
; trim_string(str)
;
;
; 
;;; =========================================================================
;;; Misc FUNCTIONS
;;; =========================================================================
;
;extract_symbol_only 
;

;
;apply_to_extracted_symbol -
;

;
;apply_updates_to_symbol -
;

;
;perform__symbol_update - 
;

;
;get_expansion_value - opens the screen and get expansion value
;

;
;Form_Action1 - used for getting expansin values
;

;
;Create_Form -
;

;
;create_expansion_form -
;

;
;get_component_by_index -
;

;
;import_csv_to_collection -
;

;
;convert_to_number -
;

;
;create_updated_csv_file  -
;




;axlCmdRegister("flir_footprint_update" 'flir_footprint_update ?cmdType "interactive")

axlCmdRegister("Main" 'flir_footprint_update )
;******************************************************
;
; Main Routine
;
;******************************************************

procedure(Main()

  prog((original_dir target_dir footprint_names unique_symbols total_count success_count duplicate_count collection)


duplicate_count = 0

;
; Create a structure to use with the excel file and the footprints.
;

Create_File_structure()						
 
;
; Check if a design is currently open if not exit program
;
    current_design = axlDBGetDesign()
    unless(current_design
        axlUIConfirm("No design is currently open.")
        println("No design is currently open.")
        return(nil)
    );End Unless
;
; Debug: Show design information
;
    ;printf("Current design: %s\n"    axlCurrentDesign())
    ;printf("Design components count: %d\n" length(current_design->components))
    ;printf("Design symbols count: %d\n" length(current_design->symbols))
;
; Check if there are any components in the design
;
    unless(current_design->components
      axlUIConfirm("No components found in the current design")
      return(nil)
    ); End unless
  ;
  ; Check if we're in the PCB Editor(not in symbol editor)
  ;
 
    unless(axlDesignType(t) == "BOARD"
      axlUIConfirm("This script must be run in PCB Editor mode, not Symbol Editor.")
      return(nil)
    ); End unless
    
    ;
    ; Set orignal and target directory for saving original symbols
    ; and new symbols
    ;

    original_dir = "./original_Library"
    target_dir = "./new_Library"
    
   ;
   ; Get PCB file name
   ;
   
   Pcb_File_Name = axlCurrentDesign()
   
;
; Get the design type
;
       design_type = axlDesignType(t)
      
;
; Get the working directory
;
       working_dir = getWorkingDir()
       
; Construct full path
    
    full_path = strcat(working_dir, "/" , Pcb_File_Name)
   
;
; If Directory for symbols does not exist create it and output message.
;!!!! Are they created ? 
;

    createDir(original_dir)
    createDir(target_dir)
;     
; if Flir_updated_components exists then get file into a variable unique_footprints otherwise 
; create the structure for unique_fooptrints
; 

;
; Check if user wants to load existing text file or start fresh
;
    
        csv_filename = "Updated_Components.csv"
        
    ; Check if CSV file exists before attempting to load it
    ; This prevents false detection of non-existent files
 
 if(isFile(csv_filename) then
 
 load_existing = axlUIYesNo(sprintf(nil "Found existing CSV file: %s\n\nDo you want to load and continue from existing data?\n\nYes = Load existing CSV and add new components\nNo = Start fresh (existing CSV will be overwritten)" csv_filename))
      if(load_existing then
        printf("Loading existing CSV file: %s\n" csv_filename)

        collection = load_collection_from_csv(csv_filename)
        if(collection then
          printf("Loaded %d existing components from CSV file\n" collection["count"])
        else
          printf("Failed to load CSV file, starting fresh\n")
          collection = create_component_collection()
        ); End if
      else
        printf("Starting with fresh collection\n")
        collection = create_component_collection()
      );end if
    else
      printf("No existing CSV file found, starting fresh\n")
      collection = create_component_collection()
    ); End if

    ;
    ; Get all footprint names in the design.
     
    footprint_names = axlDBGetDesign()->symbols~>name

    ; Debug: Show what footprint names were found
    ;printf("Footprint names found: %d\n" length(footprint_names))
    ;when(footprint_names
    ;  if(length(footprint_names) > 5 then
    ;   printf("First 5 footprints: %s %s %s %s %s ...\n"
    ;         nth(0 footprint_names) nth(1 footprint_names) nth(2 footprint_names)
    ;         nth(3 footprint_names) nth(4 footprint_names))
    ;else
    ;  printf("All footprints: %L\n" footprint_names)
    ;)
    ;)
   ;)
   
    printf("Processing %d footprint names\n" length(footprint_names))

    foreach(current_footprint_name footprint_names
      printf("\nProcessing footprint: '%s'\n" current_footprint_name)
      
      ;
      ; Check if this entry already exists as a value in the table
      ;
      existing_components = find_components_by_footprint(collection current_footprint_name)
      printf("Found %d existing components with this footprint\n" length(existing_components))
      
      if(length(existing_components) > 0 then
        ;
        ; Entry already exists as a value, count as duplicate
        ; Update pcb_name
        ;
        duplicate_count = duplicate_count + 1
        component = car(existing_components)
        printf("  Updating duplicate: old PCB name='%s', new PCB name='%s'\n" 
               component->pcb_name Pcb_File_Name)
        update_component_field(component "pcb_name" Pcb_File_Name)
      else
        ;
        ; Entry doesn't exist, add it to the table
        ; Note the consistent field order: footprint_name first, then pcb_name
        ;
        printf("  Creating new component with footprint='%s', PCB='%s'\n" 
               current_footprint_name Pcb_File_Name)
        component = create_pcb_component(
          Pcb_File_Name           ; Second field
          current_footprint_name  ; First field
          nil nil nil nil "none"
        )
        
        printf("  Adding new component to collection\n")
        add_component_to_collection(collection component)
      );End If
    ); End Foreach
    
    ;
    ; Save collection to file
    ;

save_collection_to_csv(collection "Footprint_File_list.csv")

display_collection_summary(collection duplicate_count length(footprint_names))


    
    
 ;  
 ;  
 ;
    
 ;   printf("Starting symbol extraction phase...\n")

    ; PHASE 1: Extract ALL symbols while in board mode
    
    extraction_count = 0
    components = collection["components"]

    printf("PHASE 1: Extracting %d unique footprints to directories...\n" collection["count"])

    foreach(component components
      printf("Extracting footprint: %s\n" component->footprint_name)
      when(extract_symbol_only(component target_dir)
        extraction_count = extraction_count + 1
      );End when
    );End foreach

    printf("PHASE 1 Complete: %d of %d footprints extracted\n" extraction_count collection["count"])

    ;
    ; Ask user if they want to proceed with FLIR modifications
    ;
    proceed_with_flir = axlUIYesNo("All symbols have been extracted to directories.\n\nDo you want to proceed with DISPLAY_TOP_LAYER modifications?\n\nYes = Apply DISPLAY_TOP_LAYER updates to symbols\nNo = Skip DISPLAY_TOP_LAYER updates (extraction only)")

    ;printf("Debug: User choice for proceeding: %L\n" proceed_with_flir)

    if(proceed_with_flir then
      printf("Debug: Entering FLIR modification section\n")
      ;
      ; Get FLIR expansion value from user via form AFTER user confirms they want to proceed
      ;
      printf("About to show expansion value form...\n")
    
      flir_expansion_value = get_expansion_value()
      printf("Form returned value: %L\n" flir_expansion_value)

;
; Check the result
;
      if(Increase_Value == nil then
        printf("DISPLAY_TOP_LAYER expansion selection cancelled\n")
        return(nil)
      else
        printf("Proceeding with expansion value: %f\n" Increase_Value)
        flir_expansion_value = Increase_Value
      );End if
;
; PHASE 2: Process each extracted symbol with FLIR updates
;
      success_count = 0
      
      printf("PHASE 2: Applying FLIR updates to %d extracted symbols with %fmm expansion...\n" extraction_count flir_expansion_value)
;
; Process only the unique components from the collection
;
    unique_components = collection["components"]
    processed_footprints = nil  ; Track processed footprints to avoid duplicates

    foreach(component unique_components
;
; Only process if we haven't processed this footprint already
;
      unless(member(component->footprint_name processed_footprints)
        printf("Processing FLIR updates for: %s\n" component->footprint_name)
        processed_footprints = cons(component->footprint_name processed_footprints)

        when(process_updates_to_symbol(component target_dir flir_expansion_value)
          success_count = success_count + 1
          ; Remove this line - let process_updates_to_symbol handle the display_top field
          ; update_component_field(component "display_top" "Added")
        ) ; End when

      ) ; End unless
    ) ; End foreach

      printf("Completed: %d of %d unique footprints processed with FLIR updates\n" success_count collection["count"])

    else
      ; User chose to skip FLIR modifications
      printf("FLIR modifications skipped by user choice\n")
      printf("Symbols extracted to directories without modifications\n")
      success_count = extraction_count  ; Set success count to extraction count for reporting
    ) ; End if proceed_with_flir

    ; Create new CSV file with updated data
    create_updated_csv_file(collection)

    ; Report final results
    printf("Completed: %d of %d unique symbols extracted and updated with FLIR to %s\n"
           success_count collection["count"] target_dir)

    ; Reload the original PCB file (add .brd extension if not present)
    pcb_file_with_ext = if(rexMatchp("\\.brd$" Pcb_File_Name) then Pcb_File_Name else strcat(Pcb_File_Name ".brd"))
    printf("Reloading original PCB file: %s\n" pcb_file_with_ext)
    axlShell(strcat("open " pcb_file_with_ext))
    printf("Returned to original PCB design: %s\n" pcb_file_with_ext)

  ) ; End prog
) ; End procedure

;******************************************************
;
; extract_symbol_only - Extract symbol to directories (Phase 1 - Board Mode)
;
;******************************************************

procedure(extract_symbol_only(component target_dir)
  prog((footprint_name symDef result symbol_path original_path)
    ;
    ; Get footprint name from our component structure
    ;
    footprint_name = component->footprint_name

    ; Skip if no footprint name
    unless(footprint_name
      return(nil)
    ); End unless

    ; Show progress
    printf("Extracting footprint: %s to original and new directories\n" footprint_name)

    ; Find the symbol definition for this footprint name
    ; Search through all components in design to find one with this footprint
    symDef = nil

    foreach(comp axlDBGetDesign()->components
      when(equal(comp->symbol->name footprint_name)
        symDef = comp->symbol->definition
      )
    )

    ; Check if we found the symbol definition
    unless(symDef
      printf("ERROR: Could not find symbol definition for footprint: %s\n" footprint_name)
      return(nil)
    )

    ; Check if symbol already exists to avoid overwrite prompts
    original_path = strcat("./original_Library\\" footprint_name ".dra")
    target_path = strcat(target_dir "\\" footprint_name ".dra")

    ; Extract to original directory to preserve original
    if(isFile(original_path) then
      result = t  ; Skip if already exists
    else
      result = axlWritePackageFile(symDef "./original_Library")
      unless(result
        printf("  ERROR: Failed to extract original: %s\n" footprint_name)
        return(nil)
      )
    )

    ; Also extract to target directory for FLIR processing
    unless(isFile(target_path)
      result = axlWritePackageFile(symDef target_dir)
      unless(result
        printf("  ERROR: Failed to extract FLIR copy: %s\n" footprint_name)
        return(nil)
      )
    )

    ; Return result
    return(result)
  ) ; End Prog
) ; End procedure

;******************************************************
;
; process_updates_to_symbol - Apply FLIR updates to extracted symbol (Phase 2)
;
;******************************************************

procedure(process_updates_to_symbol(component target_dir expansion_value)
  prog((footprint_name symbol_path)
    ;
    ; Get footprint name from our component structure
    ;
    footprint_name = component->footprint_name
;
; Skip if no footprint name
;
    unless(footprint_name
      return(nil)
    ); End unless
;
; Construct path to extracted symbol file
;
    symbol_path = strcat(target_dir "\\" footprint_name ".dra")
;
; Check if the file exists
;
    unless(isFile(symbol_path)
      printf("ERROR: Symbol file not found: %s\n" symbol_path)
      return(nil)
    )
;
; Apply FLIR updates to the extracted symbol
;
    when(apply_updates_to_symbol(symbol_path footprint_name expansion_value)
      printf("  OK FLIR updates applied: %s\n" footprint_name)
;
; Update component to indicate DISPLAY_TOP was created - REMOVE THIS LINE
; Let perform_symbol_update handle setting the display_top field
;
      ; update_component_field(component "display_top" "Added")
      return(t)
    );End When

    ; If we get here, the update failed
    return(nil)
  ) ; End Prog
) ; End procedure

;******************************************************
;
; apply_updates_to_symbol - Load symbol and apply FLIR updates
;
;******************************************************

procedure(apply_updates_to_symbol(symbol_path footprint_name expansion_value)
  prog((current_design_name)
;  
; Save current design name to return to it later
;
    current_design_name = axlCurrentDesign()

    printf("  Loading symbol for FLIR update: %s\n" footprint_name)
    printf("  Debug: Attempting to open symbol at: %s\n" symbol_path)
;
; Try to open the symbol file directly without closing first
;
    shell_command = sprintf(nil "open %s" symbol_path)
    printf("  Debug: 
    %s\n" shell_command)
;
; Try the shell command and capture any output
;
    shell_result = axlShell(shell_command)
    printf("  Debug: Shell command result: %L\n" shell_result)

    unless(shell_result
      printf("  ERROR: Failed to load symbol with command: %s\n" shell_command)
;
; Try alternative: open directly
;
      printf("  Debug: Trying direct open approach\n")
      unless(axlShell(shell_command)
        printf("  ERROR: All symbol loading methods failed for: %s\n" symbol_path)
        return(nil)
      )
    )
;
; Wait a moment for the symbol to load
; (In practice, you might need a more robust way to check if loading is complete)

;
; Check if we're now in Package Symbol Editor mode
;
    design_type = axlDesignType(t)
    printf("  Debug: Design type after loading: %s\n" design_type)

    unless(design_type == "PACKAGE"
      printf("  ERROR: Not in Package Symbol Editor mode after loading: %s (got: %s)\n" footprint_name design_type)
      return(nil)
    )
;
; Note: Save prompts may appear - click Yes to save changes
;

;
; Apply the FLIR updates (core logic from _SYMBOL_UPDATE.il)
;
    when(perform_symbol_update(expansion_value component)
      printf("  ? FLIR symbol update completed: %s\n" footprint_name)

      ; Delete existing files to prevent overwrite prompts
      dra_file = strcat("./new_Library/" footprint_name ".dra")
      psm_file = strcat("./new_Library/" footprint_name ".psm")
      when(isFile(dra_file)
        deleteFile(dra_file)
        printf("  Deleted existing .dra file\n")
      )
      when(isFile(psm_file)
        deleteFile(psm_file)
        printf("  Deleted existing .psm file\n")
      )

      ; Save the symbol explicitly to new_Library directory
      save_command = strcat("save ./new_Library/" footprint_name ".dra")
      axlShell(save_command)
      printf("  ? Symbol saved to new_Library: %s\n" footprint_name)

      return(t)
    )
;
; If we get here, the update failed
;
    printf("  ? FLIR symbol update failed: %s\n" footprint_name)
    return(nil)
  ); End Prog
);End

;******************************************************
;
; perform_symbol_update - Core FLIR update logic
; this oricedure finds the PLACE_BOUND_TOP min height and max height values
; it also copys the Place_bound_shape to the Display top and enlarges it btu
; the expansion value. 
; 7-9-2025-la
;******************************************************

procedure(perform_symbol_update(expansion_value component)
  prog((shapes_to_delete shapes_to_copy New_Shape shape poly exp_poly new_shape
        place_bound_shapes place_bound_count max_height_value dfa_bound_shapes dfa_bound_count
        display_top_added)
;
; Use the expansion value passed from the form selection
;
    Increase_Value = expansion_value
    display_top_added = "Not Added"  ; Default to "Not Added"
;
;printf("    Applying FLIR updates with increase value: %f\n" Increase_Value)
;

;
; Collect PLACE_BOUND data
;

    place_bound_shapes = axlDBGetShapes("PACKAGE GEOMETRY/PLACE_BOUND_TOP")
    place_bound_count = if(place_bound_shapes then length(place_bound_shapes) else 0)

;break()
;
; Get min and max height from PLACE_BOUND layer using actual symbol data
min_height_value = "None"
max_height_value = "None"

; Try to extract height using the extract file approach
when(place_bound_shapes
  ; Create a unique temporary file name
  temp_id = sprintf(nil "%d" random(9999))
  extract_cmd_file = strcat("_height_extract_" temp_id ".txt")
  extract_data_file = strcat("_height_extract_" temp_id ".dat")
  
  ; Create extract command file for height data
  cmd_file = outfile(extract_cmd_file)
  when(cmd_file
    fprintf(cmd_file "GEOMETRY\n")
    fprintf(cmd_file "CLASS = \"PACKAGE GEOMETRY\"\n")
    fprintf(cmd_file "SUBCLASS=\"PLACE_BOUND_TOP\"\n")
    fprintf(cmd_file "GEO_PACKAGE_HEIGHT_MIN\n")
    fprintf(cmd_file "GEO_PACKAGE_HEIGHT_MAX\n")
    fprintf(cmd_file "END\n")
    close(cmd_file)
    
    ; Extract the height data
    extract_result = axlExtractToFile(extract_cmd_file extract_data_file '("quiet"))
    
    ; Parse the height data using our function
    when(extract_result
      height_values = parse_height_data(extract_data_file)
      file_min_height = car(height_values)
      file_max_height = cadr(height_values)
      
      ; Only use file values if they're not "None"
      when(file_min_height != "None" min_height_value = file_min_height)
      when(file_max_height != "None" max_height_value = file_max_height)
    )
    
    ; Clean up temporary files
    when(isFile(extract_cmd_file) deleteFile(extract_cmd_file))
    when(isFile(extract_data_file) deleteFile(extract_data_file))
  )
)

; Do NOT change "None" to "0" - keep as "None"
; when(min_height_value == "None" min_height_value = "0")
; when(max_height_value == "None" max_height_value = "0")

printf("    Extracted height for %s: MIN=%s MAX=%s mm\n" 
       component->footprint_name min_height_value max_height_value)

;
; Collect DFA_BOUND data
;

    dfa_bound_shapes = axlDBGetShapes("PACKAGE GEOMETRY/DFA_BOUND_TOP")
    dfa_bound_count = if(dfa_bound_shapes then length(dfa_bound_shapes) else 0)

    ; Delete all shapes on DISPLAY_TOP layer
    shapes_to_delete = axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP")
    if(shapes_to_delete then
      axlDeleteObject(shapes_to_delete)
      printf("    Deleted existing shapes on DISPLAY_TOP layer\n")
    else
      printf("    No existing shapes on DISPLAY_TOP layer\n")
    );End If
;
; Get shape on DFA_BOUND layer
;
    shapes_to_copy = axlDBGetShapes("PACKAGE GEOMETRY/DFA_BOUND_TOP")
;
; Check if shapes exist
;
    if(length(shapes_to_copy) == 0 then
      printf("    WARNING: No shape found on DFA_BOUND_TOP layer\n")
      display_top_added = "Not Added"
      ; Still update component data even if no shapes to copy
      when(component
        update_component_field(component "place_bound_shape" place_bound_count)
        update_component_field(component "min_height" min_height_value)
        update_component_field(component "max_height" max_height_value)
        update_component_field(component "dfa_bound_shape" dfa_bound_count)
        update_component_field(component "display_top" "Not Added")
      );End when
      ; Symbol is automatically saved by Allegro when loaded
      printf("    OK Symbol processed without FLIR updates: %s\n" component->footprint_name)
      return(t)
    );End If

    if(length(shapes_to_copy) >= 2 then
      printf("    WARNING: Multiple shapes found on DFA_BOUND_TOP layer, using first one\n")
    );End If

    ; Copy shape from DFA_BOUND_LAYER
    New_Shape = axlCopyObject(shapes_to_copy ?angle 0)

    ; Check if copy succeeded
    unless(New_Shape
      printf("    ERROR: Failed to copy shape from DFA_BOUND_TOP layer\n")
      return(nil)
    ); End unless

    ; Change Layer to display top
    New_Shape = axlChangeLayer(New_Shape "PACKAGE GEOMETRY/DISPLAY_TOP")

    unless(New_Shape
      printf("    ERROR: Failed to change shape to DISPLAY_TOP layer\n")
      return(nil)
    ); End unless

    ; Get the shape on Display Top Layer for expansion
    shape = car(axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP"))

    ; Convert shape to polygon
    poly = axlPolyFromDB(shape)

    ; Expand polygon by Increase_Value
    exp_poly = car(axlPolyExpand(poly Increase_Value 'None))

    ; Delete the original shape before creating the expanded one
    axlDeleteObject(shape)

    ; Create new expanded shape
    new_shape = axlDBCreateShape(exp_poly t "PACKAGE GEOMETRY/DISPLAY_TOP")

    if(new_shape then
      printf("    ? Created expanded DISPLAY_TOP shape\n")
      display_top_added = "Added"
    else
      printf("    ? Failed to create expanded DISPLAY_TOP shape\n")
      display_top_added = "Not Added"
    )

    ; Update component with collected data
    when(component
      ; Convert counts to strings for component fields
      update_component_field(component "place_bound_shape" sprintf(nil "%d" place_bound_count))
      update_component_field(component "min_height" min_height_value)
      update_component_field(component "max_height" max_height_value)
      update_component_field(component "dfa_bound_shape" sprintf(nil "%d" dfa_bound_count))
      update_component_field(component "display_top" display_top_added)
      
      ; Use %d for integer counts in printf
      printf("    Updated component data: PLACE_BOUND=%d, MIN_HEIGHT=%s, MAX_HEIGHT=%s, DFA_BOUND=%d, DISPLAY_TOP=%s\n"
             place_bound_count
             (if min_height_value then min_height_value else "None")
             (if max_height_value then max_height_value else "None")
             dfa_bound_count
             display_top_added)
    )

    ; Save prompts handled by explicit save

    ; Return success status
    return(equal(display_top_added "Added"))
  ); End Prog
); End procedure

;******************************************************
;
; get_expansion_value - Show form to get user's expansion preference
;
;******************************************************

procedure(get_expansion_value()
  prog((form_file myform result)
    ; Create the form file exactly like the working version
    Create_Form()

    ; Set up form variables
    form_file = "./output_form.form"

    ; Initialize global variables
    Increase_Value = 0.125  ; Default to "Least"

    ; Create and display the form exactly like working version
    myform = axlFormCreate((gensym) form_file nil 'Form_Action1 nil )
    unless(myform
      printf("ERROR: Failed to create FLIR expansion form\n")
      return(nil)
    )

    ; Display and make permanent like working version
    axlFormSetField(myform "LEAST-.125MM" t)  ; Enable Least
    axlFormDisplay(myform)
    
   ; Return immediately - let the calling function handle the waiting
    return(Increase_Value)
  ); End Prog
); End procedure

;******************************************************
;
; _form_action - Handle form actions
;
;******************************************************

procedure(Form_Action1(myform)
  prog((Field)
    printf(myform->curField)
    Field = myform->curField
 
    case(myform->curField
      ("LEAST-.125MM"
        printf("Selected: Least expansion (0.125mm)\n")
        Increase_Value = 0.125
              )
      ("NOMINAL-.25MM"
        printf("Selected: Nominal expansion (0.25mm)\n")
        Increase_Value = 0.25
      )
      ("MOST-.5MM"
        printf("Selected: Most expansion (0.5mm)\n")
        Increase_Value = 0.5
      )
      ("done"
        printf("FLIR expansion confirmed: %fmm - Starting footprint processing...\n" Increase_Value)
        axlFormClose(myform)
        axlCancelEnterFun()
        return(t)
      )
      ("cancel"
        printf("FLIR expansion selection cancelled\n")
        axlFormClose(myform)
       axlCancelEnterFun()
       return(nil)
       )
    ) ; End case
  ) ; End prog
) ; End procedure

;******************************************************
;
; Create_Form - Create BNF form file for expansion selection (exact copy from working script)
;
;******************************************************

procedure(Create_Form()
  prog((form_file myform)
    drain()
    form_file = "./output_form.form"
    myform = outfile(form_file "w")

    fprintf(myform "FILE_TYPE=FORM_DEFN VERSION=2\n")
    fprintf(myform "FORM AUTOGREYTEXT\n")
    fprintf(myform "FIXED\n")
    fprintf(myform "PORT 40 60\n")
    fprintf(myform "HEADER \"FOOTPRINT UPDATE\"\n\n")
    fprintf(myform "TILE\n\n")

    fprintf(myform "## Text in FORM ## \n")
    fprintf(myform "TEXT \"Update DISPLAY_TOP from DRA_BOUND\"\n")
    fprintf(myform "TLOC 1 1\n")
    ;fprintf(myform "TEXT \"select size to include\"\n")
    ;fprintf(myform "TLOC 1 3\n")
    fprintf(myform "ENDTEXT\n")

    fprintf(myform "## Group Definition in Form## \n")
    fprintf(myform "GROUP \"DISPLAY_TOP\"\n")
    fprintf(myform "GLOC 1 3\n")
    fprintf(myform "FSIZE 10 8\n")
    fprintf(myform "ENDGROUP\n\n")
    fprintf(myform "## RadioButton Field in Group## \n")
    fprintf(myform "FIELD LEAST-.125MM\n")
    fprintf(myform "FLOC 2 5\n")
    fprintf(myform "CHECKLIST \"LEAST-.125MM\"  \"rg\"\n")
    fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
    fprintf(myform "ENDFIELD \n\n")
    fprintf(myform "## RadioButton Field in Group## \n")
    fprintf(myform "FIELD NOMINAL-.25MM\n")
    fprintf(myform "FLOC 2 7\n")
    fprintf(myform "CHECKLIST \"NOMINAL-.25MM\"  \"rg\"\n")
    fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
    fprintf(myform "ENDFIELD \n\n")
    fprintf(myform "## RadioButton Field in Group## \n")
    fprintf(myform "FIELD MOST-.5MM\n")
    fprintf(myform "FLOC 2 9\n")
    fprintf(myform "CHECKLIST \"MOST-.5MM\"  \"rg\"\n")
    fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
    fprintf(myform "ENDFIELD \n\n")

    ;fprintf(myform "## Input Field in Form## \n")
    ;fprintf(myform "FIELD Input1\n")
    ;fprintf(myform "FLOC 15 3\n")
    ;fprintf(myform "FSIZE 5 2\n")
    ;fprintf(myform "STRFILLIN 20 40\n")
    ;fprintf(myform "FGROUP \"Form1\"\n")
    ;fprintf(myform "VALUE \"\"\n")
    ;fprintf(myform "ENDFIELD \n\n\n")

    fprintf(myform "## Button Field in FORM ## \n")
    fprintf(myform "FIELD cancel\n")
    fprintf(myform "FLOC 2 13\n")
    fprintf(myform "MENUBUTTON \"Cancel\" 10 3\n")
    fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
    fprintf(myform "ENDFIELD \n\n")

    fprintf(myform "## Button Field in FORM ## \n")
    fprintf(myform "FIELD done\n")
    fprintf(myform "FLOC 15 13\n")
    fprintf(myform "MENUBUTTON \"Done\" 10 3\n")
    fprintf(myform "FGROUP \"DISPLAY_TOP\"\n")
    fprintf(myform "ENDFIELD \n\n")

    fprintf(myform "ENDTILE\n\n")
    fprintf(myform "ENDFORM\n\n")
    close(myform)
  )
)

;******************************************************
;
; create_expansion_form - Create BNF form file for expansion selection
;
;******************************************************

procedure(create_expansion_form()
  prog((form_file myform)
    form_file = "./flir_expansion_form.form"
    myform = outfile(form_file "w")

    fprintf(myform "FILE_TYPE=FORM_DEFN VERSION=2\n")
    fprintf(myform "FORM AUTOGREYTEXT\n")
    fprintf(myform "FIXED\n")
    fprintf(myform "PORT 35 20\n")
    fprintf(myform "HEADER \"FLIR FOOTPRINT UPDATE\"\n\n")
    fprintf(myform "TILE\n\n")

    fprintf(myform "## Title Text ## \n")
    fprintf(myform "TEXT \"Select DISPLAY_TOP Expansion Amount:\"\n")
    fprintf(myform "TLOC 1 1\n")
    fprintf(myform "ENDTEXT\n\n")

    fprintf(myform "## Instruction Text ## \n")
    fprintf(myform "TEXT \"Click OK to start processing with selected value\"\n")
    fprintf(myform "TLOC 1 2\n")
    fprintf(myform "ENDTEXT\n\n")

    fprintf(myform "## Group Definition ## \n")
    fprintf(myform "GROUP \"EXPANSION\"\n")
    fprintf(myform "GLOC 1 4\n")
    fprintf(myform "FSIZE 25 12\n")
    fprintf(myform "ENDGROUP\n\n")

    fprintf(myform "## Radio Button - Least ## \n")
    fprintf(myform "FIELD Least-.125MM\n")
    fprintf(myform "FLOC 2 6\n")
    fprintf(myform "CHECKLIST \"Least (0.125mm)\" \"rg\"\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## Radio Button - Nominal ## \n")
    fprintf(myform "FIELD Nominal-.25MM\n")
    fprintf(myform "FLOC 2 8\n")
    fprintf(myform "CHECKLIST \"Nominal (0.25mm)\" \"rg\"\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## Radio Button - Most ## \n")
    fprintf(myform "FIELD Most-.5MM\n")
    fprintf(myform "FLOC 2 10\n")
    fprintf(myform "CHECKLIST \"Most (0.5mm)\" \"rg\"\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## OK Button ## \n")
    fprintf(myform "FIELD OK\n")
    fprintf(myform "FLOC 5 13\n")
    fprintf(myform "MENUBUTTON \"Start Processing\" 12 2\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## Cancel Button ## \n")
    fprintf(myform "FIELD Cancel\n")
    fprintf(myform "FLOC 19 13\n")
    fprintf(myform "MENUBUTTON \"Cancel\" 8 2\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "ENDTILE\n")
    fprintf(myform "ENDFORM\n")

    close(myform)
    printf("FLIR expansion form created: %s\n" form_file)
  ); End Prog
); End procedure


;;; =========================================================================
;;; STRUCTURE DEFINITION
;;; =========================================================================

;******************************************************
;
; This is an initial create the initial structure for each entry
;
;
;******************************************************
procedure(Create_File_structure()
  ; Only define the structure if it doesn't already exist
  unless(boundp('pcb_component)
    defstruct(pcb_component
      pcb_name          ; PCB design name
      footprint_name    ; Component footprint name
      place_bound_shape ; Placement boundary shape data
      min_height        ; Minimum component height
      max_height        ; Maximum component height
      dfa_bound_shape   ; DFA boundary shape data
      display_top       ; Display top layer information
    )
  )
) ; End procedure

;******************************************************
;
; Function to create a new PCB component record
;
;******************************************************
procedure(create_pcb_component(pcb_name footprint_name place_bound min_height max_height dfa_bound display_top)
  prog((component)
    ;
    ; Create new component structure instance
    ;
    component = make_pcb_component()

    ;
    ; Set all fields
    ;
    
    component->pcb_name = pcb_name
    component->footprint_name = footprint_name
    component->place_bound_shape = place_bound
    component->min_height = min_height
    component->max_height = max_height
    component->dfa_bound_shape = dfa_bound
    component->display_top = display_top

    ;
    ; Return the created component
    ;
    return(component)
  )
)
;******************************************************
;
; Function to display component information
;
;******************************************************
procedure(display_component(component)
  prog(()
    printf("PCB Component Information:\n")
    printf("=========================\n")
   printf("PCB Name:           %s\n" component->pcb_name)
     printf("Footprint Name:     %s\n" component->footprint_name)
    printf("Place Bound Shape:  %s\n" component->place_bound_shape)
    printf("Min Height:         %s\n" component->min_height)
    printf("Max Height:         %s\n" component->max_height)
    printf("DFA Bound Shape:    %s\n" component->dfa_bound_shape)
    printf("Display Top:        %s\n" component->display_top)
    printf("\n")
  )
)

;******************************************************
;
; Function to update component fields
;
;******************************************************
procedure(update_component_field(component field_name new_value)
  prog(()
    case(field_name
      ("pcb_name"
        component->pcb_name = new_value)
      ("footprint_name"
        component->footprint_name = new_value)
      ("place_bound_shape"
        component->place_bound_shape = new_value)
      ("min_height"
        component->min_height = new_value)
      ("max_height"
        component->max_height = new_value)
      ("dfa_bound_shape"
        component->dfa_bound_shape = new_value)
      ("display_top"
        component->display_top = new_value)
      (t
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    ;printf("Updated %s to: %s\n" field_name new_value)
    return(t)
  )
)
;******************************************************
;
; Function to get component field value
;
;******************************************************
procedure(get_component_field(component field_name)
  prog((value)
    case(field_name
      ("pcb_name" 
        value = component->pcb_name)
      ("footprint_name" 
        value = component->footprint_name)
      ("place_bound_shape" 
        value = component->place_bound_shape)
      ("min_height"
        component->min_height = new_value)
      ("max_height" 
        value = component->max_height)
      ("dfa_bound_shape" 
        value = component->dfa_bound_shape)
      ("display_top" 
        value = component->display_top)
      (t 
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    return(value)
  )
)

;;; =========================================================================
;;; STRUCTURE COLLECTION MANAGEMENT
;;; =========================================================================

;
; Function to create a collection of components (using a table)
;
procedure(create_component_collection()
  prog((collection)
    ;
    ; Create table to hold multiple components
    ;
    collection = makeTable("component_collection" nil)
    collection["components"] = nil
    collection["count"] = 0

    ;
    ; Set structure field definitions for self-describing collection
    ;
    collection["structure_name"] = "pcb_component"
    collection["field_names"] = list(
      "pcb_name"
      "footprint_name"
      "place_bound_shape"
      "min_height"
      "max_height"
      "dfa_bound_shape"
      "display_top"
    )
    collection["field_count"] = length(collection["field_names"])
    collection["csv_headers"] = list(
      "PCB Name"
      "Footprint Name"
      "Place Bound Shape"
      "Min Height"
      "Max Height"
      "DFA Bound Shape"
      "Display Top"
    )
    collection["version"] = "1.0"
    collection["created_date"] = getCurrentTime()

    return(collection)
  )
)

;
; Function to add component to collection
;
procedure(add_component_to_collection(collection component)
  prog((components)
    ;
    ; Get current components list
    ;
    components = collection["components"]

    ;
    ; Add new component
    ;
    if(components then
      collection["components"] = append(components list(component))
    else
      collection["components"] = list(component)
    )

    ;
    ; Update count
    ;
    collection["count"] = collection["count"] + 1

    printf("Added component to collection. Total count: %d\n" collection["count"])
    return(t)
  )
)

;
; Function to get collection size
;
procedure(get_collection_size(collection)
  return(collection["count"])
)

;
; Function to get component from collection by index
;
procedure(get_component_by_index(collection index)
  prog((components)
    components = collection["components"]

    ;
    ; Check bounds
    ;
    if(index < 1 || index > length(components) then
      printf("Index %d out of bounds (1 to %d)\n" index length(components))
      return(nil)
    )

    return(nthelem(index components))
  )
)

;
; Function to find components by footprint name
;
procedure(find_components_by_footprint(collection footprint_name)
  prog((components matches)
    components = collection["components"]
    matches = nil
    
    foreach(comp components
      ; Check if footprint name matches (case insensitive)
      ; Use upperCase to convert both strings to uppercase for comparison
      when(upperCase(comp->footprint_name) == upperCase(footprint_name)
        matches = cons(comp matches)
      )
    )
    
    return(matches)
  )
)
; =========================================================================
; CSV EXPORT FUNCTIONALITY
; =========================================================================

;******************************************************
;
; Function to save component collection to CSV file
; Using ! as delimiters
;
;******************************************************
procedure(save_collection_to_csv(collection filename)
  prog((file_handle components place_bound_str dfa_bound_str)
    printf("Saving collection to CSV file: %s\n" filename)
    
    ; Open file for writing
    file_handle = outfile(filename)
    unless(file_handle
      printf("ERROR: Could not create CSV file: %s\n" filename)
      return(nil)
    )
    
    ; Write header with ! delimiters
    fprintf(file_handle "!PCB_Name!!Footprint_Name!!Place_Bound_Shape!!Min_Height!!Max_Height!!DFA_Bound_Shape!!Display_Top!\n")
    
    ; Get components
    components = collection["components"]
    
    ; Debug - check if components list is empty
    printf("Debug: Number of components to save: %d\n" length(components))
    
    ; Write each component with ! delimiters
    foreach(comp components
      ; Convert numeric values to strings with sprintf
      place_bound_str = if(comp->place_bound_shape then 
                          if(numberp(comp->place_bound_shape) then 
                            sprintf(nil "%d" comp->place_bound_shape)
                          else 
                            comp->place_bound_shape
                          )
                        else "0")
                        
      dfa_bound_str = if(comp->dfa_bound_shape then 
                        if(numberp(comp->dfa_bound_shape) then 
                          sprintf(nil "%d" comp->dfa_bound_shape)
                        else 
                          comp->dfa_bound_shape
                        )
                      else "0")
      
      ; Use safe string values for all fields
      fprintf(file_handle "!%s!!%s!!%s!!%s!!%s!!%s!!%s!\n"
        (if comp->pcb_name then comp->pcb_name else "")
        (if comp->footprint_name then comp->footprint_name else "")
        place_bound_str
        (if comp->min_height then comp->min_height else "None")
        (if comp->max_height then comp->max_height else "None")
        dfa_bound_str
        (if comp->display_top then comp->display_top else "Not Added")
      )
    )
    
    close(file_handle)
    printf("Collection saved to CSV file: %d components\n" collection["count"])
    return(t)
  )
)

;******************************************************
;
; create_updated_csv_file - Create CSV with updated component data
; Using ! as delimiters
;
;******************************************************
procedure(create_updated_csv_file(collection)
  prog((csv_file components component csv_filename place_bound_str dfa_bound_str)
    csv_filename = "Updated_Components.csv"

    printf("Creating updated CSV file: %s\n" csv_filename)

    ; Open CSV file for writing
    csv_file = outfile(csv_filename)
    unless(csv_file
      printf("ERROR: Could not create CSV file: %s\n" csv_filename)
      return(nil)
    )

    ; Write CSV header with consistent ! delimiters
    fprintf(csv_file "!PCB_Name!!Footprint_Name!!Place_Bound_Shapes!!Min_Height!!Max_Height!!DFA_Bound_Shapes!!Display_Top_Added!\n")

    ; Get components from collection
    components = collection["components"]
    
    ; Write each component with consistent format
    foreach(component components
      ; Convert numeric values to strings with sprintf
      place_bound_str = if(component->place_bound_shape then 
                          if(numberp(component->place_bound_shape) then 
                            sprintf(nil "%d" component->place_bound_shape)
                          else 
                            component->place_bound_shape
                          )
                        else "0")
                        
      dfa_bound_str = if(component->dfa_bound_shape then 
                        if(numberp(component->dfa_bound_shape) then 
                          sprintf(nil "%d" component->dfa_bound_shape)
                        else 
                          component->dfa_bound_shape
                        )
                      else "0")
      
      ; Use consistent format for all rows - exactly 7 fields with ! delimiters
      fprintf(csv_file "!%s!!%s!!%s!!%s!!%s!!%s!!%s!\n"
              (if component->pcb_name then component->pcb_name else "")
              (if component->footprint_name then component->footprint_name else "")
              place_bound_str
              (if component->min_height then component->min_height else "None")
              (if component->max_height then component->max_height else "None")
              dfa_bound_str
              (if component->display_top then component->display_top else "Not Added")
      )
    )
    
    close(csv_file)
    printf("Updated CSV file created: %s with %d components\n" csv_filename collection["count"])
    return(t)
  )
)

;
; Function to display collection summary
;
procedure(display_collection_summary(collection duplicate_count total_footprints)
  prog((components unique_pcbs unique_footprints)

    components = collection["components"]
    unique_pcbs = nil
    unique_footprints = nil

    printf("Component Collection Summary:\n")
    printf("============================\n")
    printf("Unique Components in Collection: %d\n" collection["count"])

    ;
    ; Count unique PCBs and footprints
    ;
    foreach(comp components
      unless(member(comp->pcb_name unique_pcbs)
        unique_pcbs = cons(comp->pcb_name unique_pcbs)
      )
      unless(member(comp->footprint_name unique_footprints)
        unique_footprints = cons(comp->footprint_name unique_footprints)
      )
    )

    printf("Unique PCBs: %d\n" length(unique_pcbs))
    printf("Unique Footprints: %d\n" length(unique_footprints))
    printf("Total Footprints Processed: %d\n" total_footprints)
    printf("Duplicate Instances Found: %d\n" duplicate_count)
    printf("\n")
  )
)

;;; =========================================================================
;;; UTILITY FUNCTIONS
;;; =========================================================================
;******************************************************
;
; Enhanced function to load components from CSV file with proper CSV parsing
;
;******************************************************

procedure(load_collection_from_csv(filename)
  prog((file_handle line collection fields comp row_count header_line)
    ;
    ; Check if file exists
    ;
    unless(isFile(filename)
      printf("CSV file does not exist: %s\n" filename)
      return(nil)
    )

    ;
    ; Open file
    ;
    file_handle = infile(filename)
    unless(file_handle
      printf("Failed to open CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Create collection
    ;
    collection = create_component_collection()
    row_count = 0

    printf("Reading CSV file: %s\n" filename)

    ;
    ; Read header line
    ;
    if(gets(header_line file_handle) then
      printf("Header: %s\n" trim_string(header_line))
    )

    ;
    ; Read data lines
    ;
    while(gets(line file_handle)
      row_count = row_count + 1
      line = trim_string(line)

      ;
      ; Skip empty lines
      ;
      unless(strlen(line) == 0
        ;
        ; Parse line
        ;
        fields = parse_csv_line(line)  ; Keep function name for now
        
        ; Only process if we have at least 2 fields
        if(length(fields) >= 2 then
          ; Extract fields
          pcb_name = nth(0 fields)
          footprint_name = nth(1 fields)
          
          ; Get optional fields if available
          place_bound = if(length(fields) > 2 then nth(2 fields) else nil)
          min_height = if(length(fields) > 3 then nth(3 fields) else nil)
          max_height = if(length(fields) > 4 then nth(4 fields) else nil)
          dfa_bound = if(length(fields) > 5 then nth(5 fields) else nil)
          display_top = if(length(fields) > 6 then nth(6 fields) else nil)
          
          ; Create component
          comp = create_pcb_component(
            pcb_name          
            footprint_name    
            place_bound
            min_height
            max_height
            dfa_bound
            display_top
          )
          add_component_to_collection(collection comp)
        else
          ; Skip row with insufficient fields
          printf("  Row %d: Skipped - insufficient fields (%d)\n" row_count length(fields))
        )
      )
    )

    close(file_handle)
    printf("CSV file import completed: %d components loaded from %d rows\n"
           collection["count"] row_count)

    return(collection)
  )
)

;
; Function to import and merge CSV data into existing collection
;
procedure(import_csv_to_collection(existing_collection filename)
  prog((imported_collection imported_components)
    ;
    ; Load CSV data
    ;
    imported_collection = load_collection_from_csv(filename)

    unless(imported_collection
      printf("Failed to import CSV data\n")
      return(nil)
    )

    ;
    ; Get imported components
    ;
    imported_components = imported_collection["components"]

    ;
    ; Add each imported component to existing collection
    ;
    foreach(comp imported_components
      add_component_to_collection(existing_collection comp)
    )

    printf("Merged %d components from CSV into existing collection\n"
           imported_collection["count"])
    return(existing_collection)
  )
)

;
; Function to parse CSV line with proper quote handling
;
procedure(parse_csv_line(line)
  prog((fields)
    ;
    ; Handle empty line
    ;
    if(strlen(line) == 0 then
      return(list("" "" "" "" "" "" ""))
    )
    
    ;
    ; Split by !! delimiter
    ;
    fields = parseString(line "!!")
    
    ;
    ; Clean up first field (remove leading !)
    ;
    when(car(fields) && substring(car(fields) 1 1) == "!"
      fields = cons(substring(car(fields) 2) cdr(fields))
    )
    
    ;
    ; Clean up last field (remove trailing !)
    ;
    when(length(fields) > 0
      last_field = nth(length(fields)-1 fields)
      when(last_field && substring(last_field strlen(last_field)) == "!"
        fields = append(butlast(fields) 
                       list(substring(last_field 1 strlen(last_field)-1)))
      )
    )
    
    ;
    ; Ensure we have exactly 7 fields (pad with empty strings if needed)
    ;
    while(length(fields) < 7
      fields = append(fields list(""))
    )
    
    return(fields)
  )
) ; End procedure

; Load message
println("Save PCB Symbols to Library Script loaded. Run 'Main' to use.")

;******************************************************
;
; parse_height_data - Extract height information from extracted data
; Returns a list with min_height and max_height as strings
;
;******************************************************
procedure(parse_height_data(data_file_path)
  prog((data_file line_data min_height max_height)
    ; Initialize height values as "None"
    min_height = "None"
    max_height = "None"
    
    ; Check if data file exists
    unless(isFile(data_file_path)
      printf("    Height data file not found: %s\n" data_file_path)
      return(list(min_height max_height))
    )
    
    ; Open data file
    data_file = infile(data_file_path)
    unless(data_file
      printf("    Failed to open height data file: %s\n" data_file_path)
      return(list(min_height max_height))
    )
    
    ; Process each line individually
    while(gets(line_data data_file)
      ; Clean the line (remove newlines/carriage returns)
      clean_line = buildString(parseString(line_data "\n") "")
      clean_line = buildString(parseString(clean_line "\r") "")
      
      printf("    Examining line: '%s'\n" clean_line)
      
      ; Only process lines that start with "S"
      when(substring(clean_line 1 1) == "S"
        ; Check for the specific pattern "S!!0.8 MM!"
        if(substring(clean_line 1 3) == "S!!" then
          ; Extract the value after "S!!" but before the last "!"
          raw_value = substring(clean_line 4)
          ; Remove trailing "!" if present
          if(substring(raw_value strlen(raw_value)) == "!" then
            max_height = substring(raw_value 1 strlen(raw_value)-1)
          else
            max_height = raw_value
          )
          printf("    Found S!! pattern, setting max_height to: '%s'\n" max_height)
        else
          ; Try normal split for other patterns
          fields = parseString(clean_line "!")
          printf("    Split into %d fields: %L\n" length(fields) fields)
          
          ; Check for S!min!max pattern
          if(length(fields) >= 3 then
            min_field = nth(1 fields)
            max_field = nth(2 fields)
            
            when(min_field && strlen(min_field) > 0
              min_height = min_field
              printf("    Setting min_height to: '%s'\n" min_height)
            )
            
            when(max_field && strlen(max_field) > 0
              max_height = max_field
              printf("    Setting max_height to: '%s'\n" max_height)
            )
          )
        )
      )
    )
    
    ; Close the file
    close(data_file)
    
    ; Add debug print to show final values
    printf("    FINAL HEIGHT VALUES: MIN='%s' MAX='%s'\n" min_height max_height)
    
    return(list(min_height max_height))
  )
) ; End procedure

;******************************************************
;
; Function to trim whitespace from string
;
;******************************************************
procedure(trim_string(str)
  prog((start_pos end_pos result)
    unless(str
      return("")
    )

    ; Find first non-space character
    start_pos = 1
    while(start_pos <= strlen(str) &&
          member(substring(str start_pos start_pos) list(" " "\t" "\n" "\r"))
      start_pos = start_pos + 1
    )

    ; Find last non-space character
    end_pos = strlen(str)
    while(end_pos >= 1 &&
          member(substring(str end_pos end_pos) list(" " "\t" "\n" "\r"))
      end_pos = end_pos - 1
    )

    ; Extract trimmed string
    if(start_pos <= end_pos then
      result = substring(str start_pos end_pos)
    else
      result = ""
    )

    return(result)
  )
) ; End procedure








