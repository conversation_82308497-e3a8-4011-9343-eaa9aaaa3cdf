;; =========================================================================
;; Update PCB Symbols Script for Cadence Allegro
;; - Extracts all symbols from current PCB into directories
;; - Creates directories if they don't exist
;; - Handles duplicate symbols
;; - Provides progress feedback during operation
;; =========================================================================

axlCmdRegister("update_pcb_symbols" 'update_pcb_symbols)

;******************************************************
;
; Main Routine
;
;******************************************************

procedure(update_pcb_symbols()
  prog((original_dir target_dir footprint_names collection duplicate_count success_count)
    ; Check if we're in the PCB Editor
    unless(axlDesignType(t) == "BOARD"
      axlUIConfirm("This script must be run in PCB Editor mode, not Symbol Editor.")
      return(nil)
    )
    
    ; Set directories for saving symbols
    original_dir = "./original_Library"
    target_dir = "./new_Library"
    
    ; Get PCB file name
    pcb_file_name = axlCurrentDesign()
    
    ; Create directories if they don't exist
    createDir(original_dir)
    createDir(target_dir)
    
    ; Create component collection
    collection = create_component_collection()
    
    ; Get all footprint names in the design
    footprint_names = axlDBGetDesign()->symbols~>name
    
    ; Process each footprint
    foreach(current_footprint_name footprint_names
      ; Check if this entry already exists in the collection
      existing_components = find_components_by_footprint(collection current_footprint_name)
      
      if(length(existing_components) > 0 then
        ; Entry already exists, count as duplicate
        duplicate_count = duplicate_count + 1
        component = car(existing_components)
        update_component_field(component "pcb_name" pcb_file_name)
      else
        ; Entry doesn't exist, add it to the collection
        component = create_pcb_component(current_footprint_name pcb_file_name nil nil nil nil)
        add_component_to_collection(collection component)
      )
    )
    
    ; Save collection to file
    save_collection_to_csv(collection "Footprint_file_list.CSV")
    
    ; Display summary
    display_collection_summary(collection duplicate_count length(footprint_names))
    
    ; Extract all symbols
    printf("PHASE 1: Extracting %d unique footprints to directories...\n" collection["count"])
    
    extraction_count = 0
    components = collection["components"]
    
    foreach(component components
      printf("Extracting footprint: %s\n" component->footprint_name)
      when(extract_symbol_only(component original_dir target_dir)
        extraction_count = extraction_count + 1
      )
    )
    
    printf("PHASE 1 Complete: %d of %d footprints extracted\n" extraction_count collection["count"])
    
    ; Create updated CSV file
    create_updated_csv_file(collection)
    
    ; Report final results
    printf("Completed: %d of %d unique symbols extracted to directories\n"
           extraction_count collection["count"])
    
    return(t)
  )
)

;******************************************************
;
; extract_symbol_only - Extract symbol to directories
;
;******************************************************

procedure(extract_symbol_only(component original_dir target_dir)
  prog((footprint_name symDef result original_path target_path)
    ; Get footprint name
    footprint_name = component->footprint_name
    
    ; Skip if no footprint name
    unless(footprint_name
      return(nil)
    )
    
    ; Show progress
    printf("Extracting footprint: %s to original and new directories\n" footprint_name)
    
    ; Find the symbol definition
    symDef = nil
    foreach(comp axlDBGetDesign()->components
      when(equal(comp->symbol->name footprint_name)
        symDef = comp->symbol->definition
      )
    )
    
    ; Check if we found the symbol definition
    unless(symDef
      printf("ERROR: Could not find symbol definition for footprint: %s\n" footprint_name)
      return(nil)
    )
    
    ; Check if symbol already exists to avoid overwrite prompts
    original_path = strcat(original_dir "\\" footprint_name ".dra")
    target_path = strcat(target_dir "\\" footprint_name ".dra")
    
    ; Extract to original directory to preserve original
    if(isFile(original_path) then
      result = t  ; Skip if already exists
    else
      result = axlWritePackageFile(symDef original_dir)
      unless(result
        printf("  ERROR: Failed to extract original: %s\n" footprint_name)
        return(nil)
      )
    )
    
    ; Also extract to target directory
    unless(isFile(target_path)
      result = axlWritePackageFile(symDef target_dir)
      unless(result
        printf("  ERROR: Failed to extract to target directory: %s\n" footprint_name)
        return(nil)
      )
    )
    
    ; Now apply FLIR updates to the target symbol
    when(result
      ; Apply FLIR updates to the extracted symbol in target directory
      apply_flir_updates_to_symbol(target_path footprint_name 5.0)
    )
    
    ; Return result
    return(result)
  )
)

; Add a new procedure to apply FLIR updates to symbols
procedure(apply_flir_updates_to_symbol(symbol_path footprint_name expansion_value)
  prog((current_design_name)
    ; Save current design name to return to it later
    current_design_name = axlCurrentDesign()
    
    printf("  Applying FLIR updates to symbol: %s\n" footprint_name)
    
    ; Try to open the symbol file
    shell_command = sprintf(nil "open %s" symbol_path)
    shell_result = axlShell(shell_command)
    
    ; Check if symbol was opened successfully
    if(shell_result then
      printf("  Debug: Shell command result: %L\n" shell_result)
      printf("  Debug: Design type after loading: %s\n" axlDesignType(t))
      
      ; Apply FLIR updates to the symbol
      when(perform_symbol_update(expansion_value nil)
        printf("  ✓ FLIR updates applied to symbol: %s\n" footprint_name)
      )
      
      ; Save the updated symbol
      axlSaveDesign()
      
      ; Return to the original design
      when(current_design_name
        axlOpenDesign(current_design_name)
      )
      
      return(t)
    else
      printf("  ERROR: Failed to open symbol: %s\n" symbol_path)
      return(nil)
    )
  )
)

; Add the perform_symbol_update procedure
procedure(perform_symbol_update(expansion_value component)
  prog((shapes_to_delete shapes_to_copy New_Shape shape poly exp_poly new_shape
        place_bound_shapes place_bound_count dfa_bound_shapes dfa_bound_count
        display_top_added extracted_min_height extracted_max_height)
    ; Use the expansion value passed from the form selection
    Increase_Value = expansion_value
    display_top_added = "no"  ; Default to no

    ; Collect PLACE_BOUND data
    place_bound_shapes = axlDBGetShapes("PACKAGE GEOMETRY/PLACE_BOUND_TOP")
    place_bound_count = if(place_bound_shapes then length(place_bound_shapes) else 0)

    ; Initialize height values explicitly to nil
    extracted_min_height = nil
    extracted_max_height = nil

    ; Try the extract file approach
    ; Create a unique temporary file name to avoid conflicts
    temp_id = sprintf(nil "%d" random(9999))
    extract_cmd_file = strcat("_height_extract_" temp_id ".txt")
    extract_data_file = strcat("_height_extract_" temp_id ".dat")

    ; Create extract command file for height data
    cmd_file = outfile(extract_cmd_file)
    when(cmd_file
      fprintf(cmd_file "GEOMETRY\n")
      fprintf(cmd_file "CLASS = \"PACKAGE GEOMETRY\"\n")
      fprintf(cmd_file "SUBCLASS=\"PLACE_BOUND_TOP\"\n")
      fprintf(cmd_file "GEO_PACKAGE_HEIGHT_MIN\n")
      fprintf(cmd_file "GEO_PACKAGE_HEIGHT_MAX\n")
      fprintf(cmd_file "END\n")
      close(cmd_file)

      ; Extract the height data
      extract_result = axlExtractToFile(extract_cmd_file extract_data_file '("quiet"))
      
      ; Read the extracted height data
      when(extract_result && isFile(extract_data_file)
        ; Read the entire file into a string
        file_contents = ""
        data_file = infile(extract_data_file)
        when(data_file
          while(gets(line data_file)
            file_contents = strcat(file_contents line "\n")
          )
          close(data_file)
          
          ; Look for the specific pattern "S!!0.8 MM!"
          if(rexMatchp("S!!0.8 MM!" file_contents) then
            printf("DEBUG: Found pattern S!!0.8 MM! in file\n")
            extracted_min_height = nil
            extracted_max_height = "0.8 MM"
          )
        )
        
        ; Clean up temporary files
        when(isFile(extract_cmd_file) deleteFile(extract_cmd_file))
        when(isFile(extract_data_file) deleteFile(extract_data_file))
      )
    )

    ; Convert to strings for printf - use explicit string conversion
    min_height_str = if(extracted_min_height then extracted_min_height else "nil")
    max_height_str = if(extracted_max_height then extracted_max_height else "nil")
    
    printf("    Extracted height values: MIN=%s MAX=%s\n" min_height_str max_height_str)

    ; Collect DFA_BOUND data
    dfa_bound_shapes = axlDBGetShapes("PACKAGE GEOMETRY/DFA_BOUND_TOP")
    dfa_bound_count = if(dfa_bound_shapes then length(dfa_bound_shapes) else 0)

    ; Delete all shapes on DISPLAY_TOP layer
    shapes_to_delete = axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP")
    if(shapes_to_delete then
      axlDeleteObject(shapes_to_delete)
      printf("    Deleted existing shapes on DISPLAY_TOP layer\n")
    else
      printf("    No existing shapes on DISPLAY_TOP layer\n")
    );End If
    
    ; Get shape on DFA_BOUND layer
    shapes_to_copy = axlDBGetShapes("PACKAGE GEOMETRY/DFA_BOUND_TOP")
    
    ; Check if shapes exist
    if(length(shapes_to_copy) == 0 then
      printf("    WARNING: No shape found on DFA_BOUND_TOP layer - trying PLACE_BOUND_TOP\n")
      ; Try PLACE_BOUND_TOP as fallback
      shapes_to_copy = axlDBGetShapes("PACKAGE GEOMETRY/PLACE_BOUND_TOP")
      if(length(shapes_to_copy) == 0 then
        printf("    WARNING: No shapes found on PLACE_BOUND_TOP either - continuing without FLIR update\n")
        display_top_added = "no_shapes_found"
        return(nil)
      )
    )

    if(length(shapes_to_copy) >= 2 then
      printf("    WARNING: Multiple shapes found on DFA_BOUND_TOP layer, using first one\n")
    )

    ; Copy shape from DFA_BOUND_LAYER
    New_Shape = axlCopyObject(shapes_to_copy ?angle 0)

    ; Check if copy succeeded
    unless(New_Shape
      printf("    ERROR: Failed to copy shape from DFA_BOUND_TOP layer\n")
      return(nil)
    )

    ; Change Layer to display top
    New_Shape = axlChangeLayer(New_Shape "PACKAGE GEOMETRY/DISPLAY_TOP")

    unless(New_Shape
      printf("    ERROR: Failed to change shape to DISPLAY_TOP layer\n")
      return(nil)
    )

    ; Get the shape on Display Top Layer for expansion
    shape = car(axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP"))

    ; Convert shape to polygon
    poly = axlPolyFromDB(shape)

    ; Expand polygon by Increase_Value
    exp_poly = car(axlPolyExpand(poly Increase_Value 'None))

    ; Delete the original shape before creating the expanded one
    axlDeleteObject(shape)

    ; Create new expanded shape
    new_shape = axlDBCreateShape(exp_poly t "PACKAGE GEOMETRY/DISPLAY_TOP")

    if(new_shape then
      printf("    ✓ Created expanded DISPLAY_TOP shape\n")
      display_top_added = "yes"
    else
      printf("    ✗ Failed to create expanded DISPLAY_TOP shape\n")
    )

    ; Update component with collected data if provided
    when(component
      update_component_field(component "place_bound_shape" place_bound_count)
      update_component_field(component "min_height" extracted_min_height)
      update_component_field(component "max_height" extracted_max_height)
      update_component_field(component "dfa_bound_shape" dfa_bound_count)
      update_component_field(component "display_top" display_top_added)
    )

    ; Return success status
    return(equal(display_top_added "yes"))
  )
)

;******************************************************
;
; PCB Component Structure and Collection Functions
;
;******************************************************

; Define PCB component structure
procedure(Create_File_structure()
  defstruct(pcb_component
    pcb_name          ; PCB design name
    footprint_name    ; Component footprint name
    place_bound_shape ; Placement boundary shape data
    max_height        ; Maximum component height
    dfa_bound_shape   ; DFA boundary shape data
    display_top       ; Display top layer information
  )
)

; Create a new PCB component record
procedure(create_pcb_component(footprint_name pcb_name place_bound max_height dfa_bound display_top)
  prog((component)
    component = make_pcb_component()
    
    component->footprint_name = footprint_name
    component->pcb_name = pcb_name
    component->place_bound_shape = place_bound
    component->max_height = max_height
    component->dfa_bound_shape = dfa_bound
    component->display_top = display_top
    
    return(component)
  )
)

; Create a collection to store components
procedure(create_component_collection()
  prog((collection)
    collection = makeTable("component_collection" nil)
    collection["components"] = nil
    collection["count"] = 0
    return(collection)
  )
)

; Add component to collection
procedure(add_component_to_collection(collection component)
  prog(()
    collection["components"] = cons(component collection["components"])
    collection["count"] = collection["count"] + 1
    return(collection)
  )
)

; Find components by footprint name
procedure(find_components_by_footprint(collection footprint_name)
  prog((components matches)
    components = collection["components"]
    matches = nil
    
    foreach(component components
      when(equal(component->footprint_name footprint_name)
        matches = cons(component matches)
      )
    )
    
    return(matches)
  )
)

; Update component field
procedure(update_component_field(component field_name new_value)
  prog(()
    case(field_name
      ("pcb_name"
        component->pcb_name = new_value)
      ("footprint_name"
        component->footprint_name = new_value)
      ("place_bound_shape"
        component->place_bound_shape = new_value)
      ("max_height"
        component->max_height = new_value)
      ("dfa_bound_shape"
        component->dfa_bound_shape = new_value)
      ("display_top"
        component->display_top = new_value)
      (t
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    return(t)
  )
)

; Display collection summary
procedure(display_collection_summary(collection duplicate_count total_count)
  prog(()
    printf("\nFootprint Collection Summary:\n")
    printf("===========================\n")
    printf("Total footprints found:    %d\n" total_count)
    printf("Unique footprints:         %d\n" collection["count"])
    printf("Duplicate footprints:      %d\n" duplicate_count)
    printf("\n")
  )
)

;******************************************************
;
; CSV File Functions
;
;******************************************************

; Save collection to CSV file
procedure(save_collection_to_csv(collection filename)
  prog((csv_file components)
    printf("Saving collection to CSV file: %s\n" filename)
    
    csv_file = outfile(filename)
    unless(csv_file
      printf("ERROR: Could not create CSV file: %s\n" filename)
      return(nil)
    )
    
    fprintf(csv_file "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n"
            "PCB_Name" "Footprint_Name" "Place_Bound_Shapes" "Max_Height" "DFA_Bound_Shapes" "Display_Top_Added")
    
    components = collection["components"]
    
    foreach(component components
      fprintf(csv_file "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n"
              component->pcb_name
              component->footprint_name
              if(component->place_bound_shape then sprintf(nil "%L" component->place_bound_shape) else "0")
              if(component->max_height then sprintf(nil "%f" component->max_height) else "0.0")
              if(component->dfa_bound_shape then sprintf(nil "%L" component->dfa_bound_shape) else "0")
              if(component->display_top then component->display_top else "no")
      )
    )
    
    close(csv_file)
    printf("Collection saved to CSV file: %s\n" filename)
    return(t)
  )
)

; Create updated CSV file with component data
procedure(create_updated_csv_file(collection)
  prog((csv_file components csv_filename)
    csv_filename = "Updated_PCB_Symbols.csv"
    
    printf("Creating updated CSV file: %s\n" csv_filename)
    
    csv_file = outfile(csv_filename)
    unless(csv_file
      printf("ERROR: Could not create CSV file: %s\n" csv_filename)
      return(nil)
    )
    
    fprintf(csv_file "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n"
            "PCB_Name" "Footprint_Name" "Place_Bound_Shapes" "Max_Height" "DFA_Bound_Shapes" "Display_Top_Added")
    
    components = collection["components"]
    
    foreach(component components
      fprintf(csv_file "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n"
              component->pcb_name
              component->footprint_name
              if(component->place_bound_shape then sprintf(nil "%L" component->place_bound_shape) else "0")
              if(component->max_height then sprintf(nil "%f" component->max_height) else "0.0")
              if(component->dfa_bound_shape then sprintf(nil "%L" component->dfa_bound_shape) else "0")
              if(component->display_top then component->display_top else "no")
      )
    )
    
    close(csv_file)
    printf("Updated CSV file created: %s with %d components\n" csv_filename collection["count"])
    return(t)
  )
)

; Load message
println("Update PCB Symbols Script loaded. Run 'update_pcb_symbols' to use.")
