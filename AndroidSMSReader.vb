Imports System.IO
Imports System.Diagnostics
Imports System.Text.RegularExpressions

Public Class AndroidSMSReader
    Private Const ADB_PATH As String = "C:\Platform-tools\adb.exe" ' Update this path to your ADB location
    Private Const OUTPUT_FILE As String = "sms_messages.txt"

    Private Structure SMSMessage
        Public Id As String
        Public Address As String
        Public Body As String
        Public Date As DateTime
        Public Type As String ' SMS_TYPE: 1 = Received, 2 = Sent
    End Structure

    Private Sub ReadMessages()
        Try
            ' Check if device is connected
            If Not IsDeviceConnected() Then
                MessageBox.Show("No Android device connected!")
                Return
            End If

            ' Get SMS messages through ADB
            Dim messages As List(Of SMSMessage) = GetSMSMessages()
            
            ' Statistics
            Dim totalMessages As Integer = messages.Count
            Dim receivedMessages As Integer = messages.Count(Function(m) m.Type = "1")
            Dim sentMessages As Integer = messages.Count(Function(m) m.Type = "2")

            ' Write to file
            Using writer As New StreamWriter(OUTPUT_FILE)
                writer.WriteLine("=== SMS Messages Report ===")
                writer.WriteLine($"Total Messages: {totalMessages}")
                writer.WriteLine($"Received Messages: {receivedMessages}")
                writer.WriteLine($"Sent Messages: {sentMessages}")
                writer.WriteLine("========================")
                writer.WriteLine()

                For Each msg In messages
                    writer.WriteLine($"Date: {msg.Date}")
                    writer.WriteLine($"Type: {If(msg.Type = "1", "Received", "Sent")}")
                    writer.WriteLine($"From/To: {msg.Address}")
                    writer.WriteLine($"Message: {msg.Body}")
                    writer.WriteLine("------------------------")
                Next
            End Using

            MessageBox.Show($"SMS messages exported to {OUTPUT_FILE}")

        Catch ex As Exception
            MessageBox.Show($"Error: {ex.Message}")
        End Try
    End Sub

    Private Function IsDeviceConnected() As Boolean
        Try
            Dim process As New Process()
            process.StartInfo.FileName = ADB_PATH
            process.StartInfo.Arguments = "devices"
            process.StartInfo.UseShellExecute = False
            process.StartInfo.RedirectStandardOutput = True
            process.StartInfo.CreateNoWindow = True
            process.Start()

            Dim output As String = process.StandardOutput.ReadToEnd()
            process.WaitForExit()

            Return output.Split(Environment.NewLine).Length > 2
        Catch ex As Exception
            Return False
        End Try
    End Function

    Private Function GetSMSMessages() As List(Of SMSMessage)
        Dim messages As New List(Of SMSMessage)
        
        Try
            ' Execute ADB command to get SMS messages
            Dim process As New Process()
            process.StartInfo.FileName = ADB_PATH
            process.StartInfo.Arguments = "shell content query --uri content://sms"
            process.StartInfo.UseShellExecute = False
            process.StartInfo.RedirectStandardOutput = True
            process.StartInfo.CreateNoWindow = True
            process.Start()

            Dim output As String = process.StandardOutput.ReadToEnd()
            process.WaitForExit()

            ' Parse the output
            For Each line In output.Split(Environment.NewLine)
                If Not String.IsNullOrEmpty(line) Then
                    Dim msg As New SMSMessage()
                    
                    ' Parse the SMS data using regex
                    msg.Id = GetValue(line, "id=(\d+)")
                    msg.Address = GetValue(line, "address=([^,]+)")
                    msg.Body = GetValue(line, "body=([^,]+)")
                    msg.Type = GetValue(line, "type=(\d+)")
                    
                    ' Parse date from timestamp
                    Dim timestamp As Long
                    If Long.TryParse(GetValue(line, "date=(\d+)"), timestamp) Then
                        msg.Date = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).LocalDateTime
                    End If

                    messages.Add(msg)
                End If
            Next
        Catch ex As Exception
            MessageBox.Show($"Error reading messages: {ex.Message}")
        End Try

        Return messages
    End Function

    Private Function GetValue(input As String, pattern As String) As String
        Dim match As Match = Regex.Match(input, pattern)
        If match.Success Then
            Return match.Groups(1).Value
        End If
        Return String.Empty
    End Function

    Private Sub btnReadMessages_Click(sender As Object, e As EventArgs) Handles btnReadMessages.Click
        ReadMessages()
    End Sub
End Class