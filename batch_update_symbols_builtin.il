;; =========================================================================
;; Batch Update Symbols Script for Cadence Allegro
;; - Uses Allegro's built-in symbol update functionality
;; - Loads each .brd file in a specified directory
;; - Updates all symbols using the built-in update command
;; - Saves and closes each file after processing
;; - Generates a summary report
;; =========================================================================

; 


axlCmdRegister("Main" 'batch_update_symbols_builtin)

procedure(Main()
  prog((form_id)
    ; Create form to get directories
    form_id = axlFormCreate(
      (list
        (list "text" "BRD Files Directory:")
        (list "field" "brd_dir" 40 "./")
        (list "text" "Symbol Library Path:")
        (list "field" "lib_path" 40 "")
        (list "text" "Update Options:")
        (list "checkbox" "update_all" "Update All Symbols" t)
        (list "checkbox" "backup" "Create Backup" t)
      )
      "Batch Update Symbols Using Built-in Commands" 
      (list "OK" "Cancel")
      'batch_update_form_callback
      t
    )
    
    ; Display the form
    axlFormDisplay(form_id)
    
    return(t)
  )
)

procedure(batch_update_form_callback(form)
  prog((brd_dir lib_path update_all backup)
    if(form->curField == "OK" then
      ; Get input values
      brd_dir = form->brd_dir
      lib_path = form->lib_path
      update_all = form->update_all
      backup = form->backup
      
      ; Process the files
      process_brd_files(brd_dir lib_path update_all backup)
    )
    
    ; Close form
    axlFormClose(form)
    return(t)
  )
)

procedure(process_brd_files(brd_dir lib_path update_all backup)
  prog((brd_files processed_count failed_count summary_list current_file result)
    
    printf("=== Batch Symbol Update Tool (Using Built-in Commands) ===\n")
    
    ; Validate directory
    unless(isDir(brd_dir)
      axlUIConfirm(sprintf(nil "BRD directory does not exist: %s" brd_dir))
      return(nil)
    )
    
    ; Find all .BRD files in the directory
    brd_files = get_brd_files_in_directory(brd_dir)
    unless(brd_files
      axlUIConfirm("No .BRD files found in the selected directory.")
      return(nil)
    )
    
    ; Confirm operation with user
    unless(axlUIYesNo(sprintf(nil "Found %d .BRD files. Process all files and update symbols?"
                             length(brd_files)))
      return(nil)
    )
    
    ; Initialize counters
    processed_count = 0
    failed_count = 0
    summary_list = nil
    
    ; Process each .BRD file
    foreach(current_file brd_files
      printf("\n=== Processing file %d of %d: %s ===\n" 
             (processed_count + failed_count + 1) length(brd_files) current_file)
      
      result = process_single_brd_file(current_file lib_path update_all backup)
      
      if(result then
        processed_count = processed_count + 1
        summary_list = cons(sprintf(nil "SUCCESS: %s" current_file) summary_list)
        printf("Successfully processed: %s\n" current_file)
      else
        failed_count = failed_count + 1
        summary_list = cons(sprintf(nil "FAILED: %s" current_file) summary_list)
        printf("Failed to process: %s\n" current_file)
      )
    )
    
    ; Display summary
    printf("\n=== Processing Summary ===\n")
    printf("Total files: %d\n" length(brd_files))
    printf("Successfully processed: %d\n" processed_count)
    printf("Failed: %d\n" failed_count)
    
    ; Write summary to file
    write_summary_to_file(summary_list "batch_update_summary.txt")
    
    return(t)
  )
)

procedure(process_single_brd_file(brd_file_path lib_path update_all backup)
  prog((original_design success)
    
    ; Store current design (if any)
    original_design = axlCurrentDesign()
    
    ; Close current design if open
    when(original_design
      printf("Closing current design: %s\n" original_design)
      axlClose()
    )
    
    ; Open the .BRD file
    printf("Opening: %s\n" brd_file_path)
    unless(axlOpen(brd_file_path)
      printf("ERROR: Failed to open file: %s\n" brd_file_path)
      return(nil)
    )
    
    ; Update symbols using built-in command
    printf("Updating symbols using built-in command\n")
    success = update_symbols_builtin(lib_path update_all backup)
    
    ; Save the design if successful
    when(success
      printf("Saving updated design\n")
      axlSaveDesign()
    )
    
    ; Close the design
    axlClose()
    
    return(success)
  )
)

procedure(update_symbols_builtin(lib_path update_all backup)
  prog((command)
    ; Construct the update command based on options
    command = "update symbols"
    
    ; Add library path if specified
    when(lib_path && lib_path != ""
      command = strcat(command " -path \"" lib_path "\"")
    )
    
    ; Add update all option if selected
    when(update_all
      command = strcat(command " -all")
    )
    
    ; Add backup option if selected
    when(backup
      command = strcat(command " -backup")
    )
    
    printf("Executing command: %s\n" command)
    
    ; Execute the command
    result = axlShell(command)
    
    ; Check result
    if(result then
      printf("Symbol update completed successfully\n")
      return(t)
    else
      printf("Symbol update failed\n")
      return(nil)
    )
  )
)

procedure(get_brd_files_in_directory(dir)
  prog((files brd_files)
    ; Get all files in directory
    files = getDirFiles(dir)
    
    ; Filter for .BRD files
    brd_files = nil
    foreach(file files
      when(stringEndsWith(lowerCase(file) ".brd")
        brd_files = cons(strcat(dir "/" file), brd_files)
      )
    )
    
    return(brd_files)
  )
)

procedure(write_summary_to_file(summary_list filename)
  prog((file)
    file = outfile(filename "w")
    unless(file
      printf("ERROR: Could not create summary file: %s\n" filename)
      return(nil)
    )
    
    fprintf(file "=== Batch Symbol Update Summary ===\n")
    fprintf(file "Date: %s\n\n" getCurrentTime())
    
    ; Write each summary line
    foreach(line reverse(summary_list)
      fprintf(file "%s\n" line)
    )
    
    close(file)
    printf("Summary written to: %s\n" filename)
    return(t)
  )
)

procedure(getCurrentTime()
  prog((time_cmd result)
    time_cmd = "date '+%Y-%m-%d %H:%M:%S'"
    result = axlShell(time_cmd)
    return(result)
  )
)

; Helper function to check if string ends with a specific suffix
procedure(stringEndsWith(str suffix)
  prog((str_len suffix_len)
    str_len = strlen(str)
    suffix_len = strlen(suffix)
    
    if(str_len < suffix_len then
      return(nil)
    )
    
    return(substring(str (str_len - suffix_len + 1) str_len) == suffix)
  )
)

; Load message
println("Batch Update Symbols Script (Using Built-in Commands) loaded. Run 'Main' to use.")