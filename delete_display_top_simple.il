;**************************************************************************
; delete_display_top_simple.il
; Simplified version to delete lines on PACKAGE GEOMETRY/DISPLAY_TOP layer
;**************************************************************************

; To load this program:
; 1. From Allegro command line: load("e:/pads/allegro/skill/ai/delete_display_top_simple.il")
;
; After loading, run by typing "delete_display_top" in the Allegro command line

axlCmdRegister("delete_display_top" 'delete_display_top ?cmdType "interactive")

procedure(delete_display_top()
  prog((original_enabled_filters original_on_buttons saveVis lines_to_delete)
    printf("Starting deletion of lines on PACKAGE GEOMETRY/DISPLAY_TOP layer\n")
    
    ; Save current find filter settings
   ; original_enabled_filters = axlGetFindFilter(t)
    ;original_on_buttons = axlGetFindFilter(nil)
    
    ; Save current visibility settings
    saveVis = axlVisibleGet()
    
    printf("Saved original find filter and visibility settings\n")
    
    ; Turn off all layers
    axlVisibleDesign(nil)
    
    ; Turn on only the target layer
    axlVisibleLayer("PACKAGE GEOMETRY/DISPLAY_TOP" t)
    axlVisibleUpdate(t)
    
    ; First disable all items in the find filter
    ; Then set the find filter to only show lines
    (axlSetFindFilter ?enabled (list "lines") ?onButtons (list "lines"))
    
    ; Clear any existing selection
    axlClearSelSet()
    
    ; Use axlAddSelectAll to select all visible objects (lines on our target layer)
    axlAddSelectAll()
    
    ; Get the selection set
    lines_to_delete = axlGetSelSet()
    
    ; Delete the lines if any were found
    if(lines_to_delete then
      printf("Found %d lines to delete\n" length(lines_to_delete))
      axlDeleteObject(lines_to_delete)
      printf("Deleted %d lines\n" length(lines_to_delete))
      
      ; Add confirmation message
      axlUIConfirm(sprintf(nil "%d lines on PACKAGE GEOMETRY/DISPLAY_TOP layer have been deleted." 
                          length(lines_to_delete)))
    else
      printf("No lines found on PACKAGE GEOMETRY/DISPLAY_TOP layer\n")
      axlUIConfirm("No lines found on PACKAGE GEOMETRY/DISPLAY_TOP layer.")
    )
    
    ; Restore original find filter settings
  ;  when(original_enabled_filters
  ;    axlSetFindFilter(?enabled original_enabled_filters)
  ;  )
  ;  when(original_on_buttons
  ;    axlSetFindFilter(?onButtons original_on_buttons)
  ;  )
    
    ; Restore original visibility settings
    axlVisibleSet(saveVis)
    
    printf("Restored original find filter and visibility settings\n")
    printf("Deletion complete\n")
    return(t)
  )
)





