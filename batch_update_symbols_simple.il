;; =========================================================================
;; Simple Batch Update Symbols Script for Cadence Allegro
;; - Uses Allegro's built-in symbol update functionality
;; - Processes all .brd files in the current directory
;; - Uses library path already set in Allegro preferences
;; - Updates all symbols and creates backups
;; - Saves and closes each file after processing
;; - Generates a summary report
;; =========================================================================


;  load("e:/pads/Allegro/Skill/ai/batch_update_symbols_simple.il")

axlCmdRegister("Main" 'Main)

procedure(Main()
  prog((brd_files processed_count failed_count summary_list current_file result)
    printf("=== Simple Batch Symbol Update Tool ===\n")
    
    ; Get current directory
    current_dir = getWorkingDir()
    printf("Working in directory: %s\n" current_dir)
    
    ; Find all .BRD files in the current directory
    brd_files = get_brd_files_in_directory(current_dir)
    unless(brd_files
      axlUIConfirm("No .BRD files found in the current directory.")
      return(nil)
    )
    
    ; Confirm operation with user
    unless(axlUIYesNo(sprintf(nil "Found %d .BRD files. Process all files and update symbols?"
                             length(brd_files)))
      return(nil)
    )
    
    ; Initialize counters
    processed_count = 0
    failed_count = 0
    summary_list = nil
    
    ; Process each .BRD file
    foreach(current_file brd_files
      printf("\n=== Processing file %d of %d: %s ===\n" 
             (processed_count + failed_count + 1) length(brd_files) current_file)
      
      result = process_single_brd_file(current_file)
      
      if(result then
        processed_count = processed_count + 1
        summary_list = cons(sprintf(nil "SUCCESS: %s" current_file) summary_list)
        printf("Successfully processed: %s\n" current_file)
      else
        failed_count = failed_count + 1
        summary_list = cons(sprintf(nil "FAILED: %s" current_file) summary_list)
        printf("Failed to process: %s\n" current_file)
      )
    )
    
    ; Display summary
    printf("\n=== Processing Summary ===\n")
    printf("Total files: %d\n" length(brd_files))
    printf("Successfully processed: %d\n" processed_count)
    printf("Failed: %d\n" failed_count)
    
    ; Write summary to file
    write_summary_to_file(summary_list "batch_update_summary.txt")
    
    return(t)
  )
)

procedure(process_single_brd_file(brd_file_path)
  prog((original_design success)
    ; Store current design (if any)
    original_design = axlCurrentDesign()
    
    ; Close current design if open
    when(original_design
      printf("Closing current design: %s\n" original_design)
      ; Use axlShell with "save" command to save and close the current design
      axlShell("save")
    )
    
    ; Open the .BRD file
    printf("Opening: %s\n" brd_file_path)
    ; Use axlShell with "open" command to open the design file
    axlShell(strcat("open " brd_file_path))
    
    ; Verify the design was opened
    unless(axlCurrentDesign()
      printf("ERROR: Failed to open file: %s\n" brd_file_path)
      return(nil)
    )
    
    ; Update symbols using built-in command
    printf("Updating symbols using built-in command\n")
    success = update_symbols_builtin()
    
    ; Save the design if successful
    when(success
      printf("Saving updated design\n")
      axlSaveDesign()
    )
    
    ; Close the design without exiting Allegro
    axlShell("save")
    
    return(success)
  )
)

procedure(update_symbols_builtin()
  prog((command result)
    ; Use the built-in update command with default options
    command = "update symbols -all -backup"
    
    printf("Executing command: %s\n" command)
    
    ; Execute the command
    result = axlShell(command)
    
    ; Check result
    if(result then
      printf("Symbol update completed successfully\n")
      return(t)
    else
      printf("Symbol update failed\n")
      return(nil)
    )
  )
)

procedure(get_brd_files_in_directory(dir)
  prog((files brd_files)
    ; Get all files in directory
    files = getDirFiles(dir)
    
    ; Filter for .BRD files
    brd_files = nil
    foreach(file files
      when(stringEndsWith(lowerCase(file) ".brd")
        brd_files = cons(strcat(dir "/" file), brd_files)
      )
    )
    
    return(brd_files)
  )
)

procedure(write_summary_to_file(summary_list filename)
  prog((file)
    file = outfile(filename "w")
    unless(file
      printf("ERROR: Could not create summary file: %s\n" filename)
      return(nil)
    )
    
    fprintf(file "=== Batch Symbol Update Summary ===\n")
    fprintf(file "Date: %s\n\n" getTimeStampString())
    
    ; Write each summary line
    foreach(line reverse(summary_list)
      fprintf(file "%s\n" line)
    )
    
    close(file)
    printf("Summary written to: %s\n" filename)
    return(t)
  )
)

procedure(getTimeStampString()
  prog((current_time)
    ; Use SKILL's built-in date/time functions instead of shell commands
    current_time = getCurrentTime()
    return(sprintf(nil "%d-%02d-%02d %02d:%02d:%02d" 
                  current_time->year 
                  current_time->month 
                  current_time->day
                  current_time->hour
                  current_time->minute
                  current_time->second))
  )
)

; Helper function to check if string ends with a specific suffix
procedure(stringEndsWith(str suffix)
  prog((str_len suffix_len)
    str_len = strlen(str)
    suffix_len = strlen(suffix)
    
    if(str_len < suffix_len then
      return(nil)
    )
    
    return(substring(str (str_len - suffix_len + 1) str_len) == suffix)
  )
)

; Load message
println("Batch Update Symbols Script loaded. Run 'Main' to use.")










