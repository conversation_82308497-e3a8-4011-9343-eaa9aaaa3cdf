;**************************************************************************
; delete_display_top.il
; This program deletes lines on PACKAGE GEOMETRY/DISPLAY_TOP layer
; using Allegro command shell
;**************************************************************************

axlCmdRegister("delete_display_top" 'delete_display_top ?cmdType "interactive")

procedure(delete_display_top()
  prog(()
    printf("Starting deletion of lines on PACKAGE GEOMETRY/DISPLAY_TOP layer\n")
    
    ; Use axlShell to run the Allegro commands directly
    axlShell("setwindow pcb")
    axlShell("version 17.4")
    
    ; Open find filter and reset all options first
    axlShell("find")
    axlShell("setwindow Form.find")
    axlShell("FORM find all_off")
    
    ; Set up the find filter to select only lines
    axlShell("FORM find lines YES")
    axlShell("FORM find done")
    
    ; Select all lines on the target layer
    axlShell("setwindow pcb")
    axlShell("generaledit")
    axlShell("setwindow Form.mini")
    axlShell("FORM mini class PACKAGE GEOMETRY")
    axlShell("FORM mini subclass DISPLAY_TOP")
    axlShell("FORM mini all_all YES")
    
    ; Delete the selected objects
    axlShell("setwindow pcb")
    axlShell("delete")
    
    ; Add confirmation message
    axlUIConfirm("Lines on PACKAGE GEOMETRY/DISPLAY_TOP layer have been deleted.")
    
    printf("Deletion of lines complete\n")
    return(t)
  )
)

; To use this program:
; 1. Load it with (load "delete_display_top.il")
; 2. Run it by typing "delete_display_top" in the Allegro command line




